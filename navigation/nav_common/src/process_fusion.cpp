#include "process_fusion.hpp"

#include "opencv2/opencv.hpp"
#include "utils/logger.hpp"

#include <map>
#include <string>

namespace fescue_iox
{

/**
 * @brief 获取草地类型的字符串描述
 * @param cell_type 草地单元格类型值
 * @return 对应的类型描述字符串
 */
std::string GetGrassCellTypeDescription(uint8_t cell_type)
{
    switch (cell_type)
    {
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_GRASS:
        return "草地";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_UNKNOWN_OBSTACLE:
        return "未知类别障碍物";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_PERSON:
        return "人";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_CAT:
        return "猫";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_DOG:
        return "狗";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_HEDGEHOG:
        return "刺猬";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_LIMIT:
        return "禁区标志";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_MARK:
        return "跨区信标";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_CHARGESTATION:
        return "充电桩";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_CHARGESTARTIONHEAD:
        return "充电桩头";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_DOWN_STEP:
        return "下沉台阶";
    case FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_PUDDLE:
        return "水坑/泳池/水塘";
    default:
        return "未知类型(" + std::to_string(cell_type) + ")";
    }
}

void GetFusionGrassDetectStatus(const fescue_msgs__msg__PerceptionFusionResult &data, GrassDetectStatus &status)
{
    switch (data.boundary_state)
    {
    case 0:
        status = GrassDetectStatus::NO_GRASS; // No grass (all obstacles)
        LOG_INFO_THROTTLE(1000, "[GetFusionGrassDetectStatus1]No grass");
        break;
    case 1:
        status = GrassDetectStatus::HAVE_GRASS_NO_OBSTACLE; // Grass present, no obstacles (all grass)
        LOG_INFO_THROTTLE(1000, "[GetFusionGrassDetectStatus1]All grass");
        break;
    case 2:
        status = GrassDetectStatus::HAVE_GRASS_HAVE_OBSTACLE; // Grass and obstacles present (partial grass, partial obstacles)
        LOG_INFO_THROTTLE(1000, "[GetFusionGrassDetectStatus1]Partial grass, partial obstacles");
        break;
    default:
        LOG_INFO_THROTTLE(1000, "[GetFusionGrassDetectStatus1] Invalid perception grass detect status: {}", data.boundary_state);
        status = GrassDetectStatus::NO_GRASS;
        break;
    }
}

void GetFusionObstacleResult(const fescue_msgs__msg__PerceptionFusionResult &data, ObstacleResult &result)
{
    float left_min_distance = data.left_min_distance;
    float ahead_min_distance = data.ahead_min_distance;
    float right_min_distance = data.right_min_distance;
    result.left_obstacle_status = data.left_boundary == 0 ? ObstacleDetectStatus::NO_OBSTACLE : ObstacleDetectStatus::HAVE_OBSTACLE;
    result.ahead_obstacle_status = data.ahead_boundary == 0 ? ObstacleDetectStatus::NO_OBSTACLE : ObstacleDetectStatus::HAVE_OBSTACLE;
    result.right_obstacle_status = data.right_boundary == 0 ? ObstacleDetectStatus::NO_OBSTACLE : ObstacleDetectStatus::HAVE_OBSTACLE;
    result.obstacle_distance = (left_min_distance < ahead_min_distance)
                                   ? ((left_min_distance < right_min_distance) ? left_min_distance : right_min_distance)
                                   : ((ahead_min_distance < right_min_distance) ? ahead_min_distance : right_min_distance);
}

void GetFusionBoundaryResult(const fescue_msgs__msg__PerceptionFusionResult &data, BoundaryResult &result)
{
    result.inverse_perspect_mask.width = data.inverse_perspect_mask.width;
    result.inverse_perspect_mask.height = data.inverse_perspect_mask.height;
    result.inverse_perspect_mask.size = data.inverse_perspect_mask.step * data.inverse_perspect_mask.height;
    result.inverse_perspect_mask.pixels_to_meters = data.pixels_to_meters;
    result.inverse_perspect_mask.image = cv::Mat(data.inverse_perspect_mask.height, data.inverse_perspect_mask.width, CV_8UC1,
                                                 (void *)data.inverse_perspect_mask.data.data());
    // cv::imwrite("/userdata/log/image/inverse_perspect_mask.png", result.inverse_perspect_mask.image);
}

void GetFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result)
{
    result.width = data.bev_grass_region.width;
    result.height = data.bev_grass_region.height;
    result.resolution = data.bev_grass_region.resolution;
    result.grid.clear();

#if 1
    for (int j = 0; j < result.height; j++)
    {
        std::vector<uint8_t> cells_array;
        cells_array.reserve(result.width);
        for (int i = 0; i < result.width; i++)
        {
            // LOG_INFO("******* {}", data.bev_grass_region.cells_array[j * result.width + i]);
            uint8_t grass_val = (data.bev_grass_region.cells_array[j * result.width + i] == FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_GRASS) ? 0 : 1;
            cells_array.push_back(grass_val);
        }
        result.grid.push_back(cells_array);
    }
#endif

#if 0
    // result.cells_array = data.bev_grass_region.cells_array;
    int idx = -1;
    // std::vector<uint8_t> std_vec(data.bev_grass_region.cells_array.begin(), data.bev_grass_region.cells_array.begin() + data.bev_grass_region.cells_array.size());
    for (int i = 0; i < result.height; i++)
    {
        // std::vector<uint8_t> vector_row;
        for (int j = 0; j < result.width; j++)
        {
            idx = j + i * result.width;
            //std::cout << idx;
            // vector_row.push_back(result.cells_array[idx]);
            result.cells_array.push_back(data.bev_grass_region.cells_array[idx]);
            // std::cout << std_vec[idx];
        }
        std::cout << std::endl;
        // result.grid.push_back(vector_row);
    }
#endif

#if 0
    LOG_ERROR("data width: {}", data.bev_grass_region.width);
    LOG_ERROR("Occupancy width: {}", result.width);
    LOG_ERROR("data height: {}", data.bev_grass_region.height);
    LOG_ERROR("Occupancy heigth: {}", result.height);
    LOG_ERROR("data resolution: {}", data.bev_grass_region.resolution);
    LOG_ERROR("Occupancy resolution: {}", result.resolution);
    LOG_ERROR("result.grid size: {}", result.grid.size());
    LOG_ERROR("result.grid size size: {}", result.grid[0].size());
    for (int i = 0; i < result.height; i++)
    {
        for (int j = 0; j < result.width; j++)
        {
            // std::cout << result.grid[i][j];
            if (result.grid[i][j] == 0)
            {
                std::cout << 0;
            }
            else
            {
                std::cout << 1;
            }
        }
        std::cout << std::endl;
    }
#endif
}

void GetOptFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result)
{
    result.width = data.bev_grass_region.width;
    result.height = data.bev_grass_region.height;
    result.resolution = data.bev_grass_region.resolution;
    result.grid.clear();
    result.opt_status = data.opt_status;

#if 1
    // if (data.opt_status)
    // {
    //     LOG_INFO("Pitch is Big And We Use Opt Fusion");
    //     for (int j = 0; j < result.height; j++)
    //     {
    //         std::vector<uint8_t> cells_array;
    //         cells_array.reserve(result.width);
    //         for (int i = 0; i < result.width; i++)
    //         {
    //             // LOG_INFO("******* {}", data.bev_grass_region.cells_array[j * result.width + i]);
    //             uint8_t grass_val = (data.bev_grass_region_opt.cells_array[j * result.width + i] == 0) ? 0 : 1;
    //             cells_array.push_back(grass_val);
    //         }
    //         result.grid.push_back(cells_array);
    //     }
    // }
    // else
    {
        for (int j = 0; j < result.height; j++)
        {
            std::vector<uint8_t> cells_array;
            cells_array.reserve(result.width);
            for (int i = 0; i < result.width; i++)
            {
                uint8_t grass_val = (data.bev_grass_region.cells_array[j * result.width + i] == FESCUE_MSGS_ENUM_GRASS_CELL_TYPE_GRASS) ? 0 : 1;
                cells_array.push_back(grass_val);
            }
            result.grid.push_back(cells_array);
        }
    }

#endif

#if 0
    // result.cells_array = data.bev_grass_region.cells_array;
    int idx = -1;
    // std::vector<uint8_t> std_vec(data.bev_grass_region.cells_array.begin(), data.bev_grass_region.cells_array.begin() + data.bev_grass_region.cells_array.size());
    for (int i = 0; i < result.height; i++)
    {
        // std::vector<uint8_t> vector_row;
        for (int j = 0; j < result.width; j++)
        {
            idx = j + i * result.width;
            //std::cout << idx;
            // vector_row.push_back(result.cells_array[idx]);
            result.cells_array.push_back(data.bev_grass_region.cells_array[idx]);
            // std::cout << std_vec[idx];
        }
        std::cout << std::endl;
        // result.grid.push_back(vector_row);
    }
#endif

#if 0
    LOG_ERROR("data width: {}", data.bev_grass_region.width);
    LOG_ERROR("Occupancy width: {}", result.width);
    LOG_ERROR("data height: {}", data.bev_grass_region.height);
    LOG_ERROR("Occupancy heigth: {}", result.height);
    LOG_ERROR("data resolution: {}", data.bev_grass_region.resolution);
    LOG_ERROR("Occupancy resolution: {}", result.resolution);
    LOG_ERROR("result.grid size: {}", result.grid.size());
    LOG_ERROR("result.grid size size: {}", result.grid[0].size());
    for (int i = 0; i < result.height; i++)
    {
        for(int j = 0; j < result.width ; j++)
        {
            // std::cout << result.grid[i][j];
            if(result.grid[i][j] == 0){
                std::cout << 0;
            }
            else
            {
                std::cout << 1;
            }
        }
        std::cout<<std::endl;
    }
#endif
}

std::shared_ptr<GridMap<uint8_t>> GetRobotGridMap(const OccupancyResult &occupancy_result)
{
    if (occupancy_result.width <= 0 || occupancy_result.resolution < 0)
    {
        LOG_WARN("occupancy result: width={} resolution={}", occupancy_result.width, occupancy_result.resolution);
        return nullptr;
    }
    double resolution = 0.01;
    int width = 400;
    int height = width;
    double width_length = width * resolution;
    double height_length = height * resolution;
    Point2f origin(0, 0);
    origin.x = -width_length / 2;
    origin.y = -height_length / 2;
    // bev坐标偏置
    double bev_x_offset = 1.05;
    double bev_y_offset = 0.5;
    auto grid_map = std::make_shared<GridMap<uint8_t>>(resolution, width, height, origin);
    for (int i = 0; i < occupancy_result.height; i++)
    {
        for (int j = 0; j < occupancy_result.width; j++)
        {
            double bev_map_x = j * occupancy_result.resolution;
            double bev_map_y = i * occupancy_result.resolution;
            double robot_map_x = bev_map_y + bev_x_offset;
            double robot_map_y = -bev_map_x + bev_y_offset;
            Point2f robot_point(robot_map_x, robot_map_y);
            const auto &grid = grid_map->ConvertToGrid(robot_point);
            if (!grid_map->IsInside(grid))
            {
                continue;
            }
            grid_map->AddData(grid, occupancy_result.grid[i][j]);
        }
    }
    return grid_map;
}

std::pair<std::shared_ptr<GridMapBase>, std::vector<std::pair<Point2f, bool>>> GetRobotMapData(const OccupancyResult &occupancy_result,
                                                                                               float x_min, float x_max, float y_min, float y_max)
{
    std::vector<std::pair<Point2f, bool>> map_data;
    if (occupancy_result.width <= 0 || occupancy_result.resolution < 0)
    {
        LOG_WARN("occupancy result: width={} resolution={}", occupancy_result.width, occupancy_result.resolution);
        return std::make_pair(nullptr, map_data);
    }
    double resolution = 0.01;
    int width = 400;
    int height = width;
    double width_length = width * resolution;
    double height_length = height * resolution;
    Point2f origin(0, 0);
    origin.x = -width_length / 2;
    origin.y = -height_length / 2;
    // bev坐标偏置
    double bev_x_offset = 1.05;
    double bev_y_offset = 0.5;
    auto grid_map = std::make_shared<GridMapBase>(resolution, width, height, origin);
    for (int i = 0; i < occupancy_result.height; i++)
    {
        for (int j = 0; j < occupancy_result.width; j++)
        {
            double bev_map_x = j * occupancy_result.resolution;
            double bev_map_y = i * occupancy_result.resolution;
            double robot_map_x = bev_map_y + bev_x_offset;
            double robot_map_y = -bev_map_x + bev_y_offset;
            Point2f robot_point(robot_map_x, robot_map_y);
            const auto &grid = grid_map->ConvertToGrid(robot_point);
            if (!grid_map->IsInside(grid))
            {
                continue;
            }
            if (occupancy_result.grid[i][j] != 0)
            {
                // 所有的障碍物点
                map_data.emplace_back(robot_point, true);
            }
            else
            {
                bool is_inside_region = robot_point.x > x_min && robot_point.x < x_max && robot_point.y > y_min && robot_point.y < y_max;
                if (is_inside_region)
                {
                    // 在指定区域内的非障碍物点
                    map_data.emplace_back(robot_point, false);
                }
            }
        }
    }
    return std::make_pair(grid_map, map_data);
}

std::shared_ptr<SDFMap> GetSDFMap(const OccupancyResult &occupancy_result, const Pose2f &cur_pose)
{
    double resolution = 0.01;
    int width = 300;
    int height = width;
    double width_length = width * resolution;
    double height_length = height * resolution;
    Point2f map_origin(cur_pose.x, cur_pose.y);
    map_origin.x = map_origin.x - width_length / 2;
    map_origin.y = map_origin.y - height_length / 2;
    auto sdf_map = std::make_shared<SDFMap>(resolution, width, height, map_origin);
    // LOG_INFO("x min: {}, y min: {}, x max: {}, y max: {} occupancy width: {}, height: {}",
    //          map_origin.x, map_origin.y, map_origin.x + width_length, map_origin.y + height_length, occupancy_result.width, occupancy_result.height);
    if (occupancy_result.width <= 0 || occupancy_result.resolution < 0)
    {
        LOG_WARN("occupancy result: width={} resolution={}", occupancy_result.width, occupancy_result.resolution);
        sdf_map->Commit();
        return sdf_map;
    }
    // bev坐标偏置
    double bev_x_offset = 1.05;
    double bev_y_offset = 0.5;
    double cos_theta = cos(cur_pose.theta);
    double sin_theta = sin(cur_pose.theta);
    for (int i = 0; i < occupancy_result.height; i++)
    {
        for (int j = 0; j < occupancy_result.width; j++)
        {
            if (occupancy_result.grid[i][j] == 0)
            {
                continue;
            }
            double bev_map_x = j * occupancy_result.resolution;
            double bev_map_y = i * occupancy_result.resolution;
            double robot_map_x = bev_map_y + bev_x_offset;
            double robot_map_y = -bev_map_x + bev_y_offset;
            Point2f robot_point(robot_map_x, robot_map_y);
            double map_x = cur_pose.x + robot_point.x * cos_theta - robot_point.y * sin_theta;
            double map_y = cur_pose.y + robot_point.x * sin_theta + robot_point.y * cos_theta;
            Point2f map_point(map_x, map_y);
            const auto &grid = sdf_map->ConvertToGrid(map_point);
            if (!sdf_map->IsInside(grid))
            {
                continue;
            }
            sdf_map->AddPoint(grid);
        }
    }
    sdf_map->Commit();
    return sdf_map;
}

void GetDetailedFusionOccupancyResult(const fescue_msgs__msg__PerceptionFusionResult &data, OccupancyResult &result)
{
    result.width = data.bev_grass_region.width;
    result.height = data.bev_grass_region.height;
    result.resolution = data.bev_grass_region.resolution;
    result.grid.clear();
    result.opt_status = data.opt_status;
    result.cells_array.clear();

    // Count the number of each cell type
    std::map<uint8_t, int> type_count;

    // Parse all types of grass cell data
    for (int j = 0; j < result.height; j++)
    {
        std::vector<uint8_t> cells_array;
        cells_array.reserve(result.width);
        for (int i = 0; i < result.width; i++)
        {
            uint8_t cell_value = data.bev_grass_region.cells_array[j * result.width + i];
            cells_array.push_back(cell_value);

            // Count type occurrences
            type_count[cell_value]++;
        }
        result.grid.push_back(cells_array);
    }

    // // Fill the one-dimensional array with complete type information
    // result.cells_array.reserve(result.width * result.height);
    // for (int j = 0; j < result.height; j++)
    // {
    //     for (int i = 0; i < result.width; i++)
    //     {
    //         uint8_t cell_value = data.bev_grass_region.cells_array[j * result.width + i];
    //         result.cells_array.push_back(cell_value);
    //     }
    // }

    // // Print statistics (optional, for debugging)
    // LOG_INFO("=== Grass Cell Type Statistics ===");
    // LOG_INFO("Grid map size: {}x{}, resolution: {:.3f}m", result.width, result.height, result.resolution);
    // for (const auto &pair : type_count)
    // {
    //     if (pair.second > 0)
    //     {
    //         LOG_INFO("Type {}: {} cells ({})",
    //                  pair.first, pair.second, GetGrassCellTypeDescription(pair.first));
    //     }
    // }
    // LOG_INFO("========================");
}

} // namespace fescue_iox
