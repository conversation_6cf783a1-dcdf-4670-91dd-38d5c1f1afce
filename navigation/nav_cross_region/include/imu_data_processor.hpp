#ifndef IMU_DATA_PROCESSOR_HPP
#define IMU_DATA_PROCESSOR_HPP

#include "data_type.hpp"
#include "utils/logger.hpp"

#include <atomic>
#include <chrono>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <vector>

namespace fescue_iox
{

/**
 * @brief IMU Data Processor Parameters Structure
 */
struct ImuProcessorParam
{
    // Filter parameters
    float filter_alpha{1.0f};                // Low-pass filter coefficient (0-1, smaller means stronger filtering)
    float angular_velocity_threshold{0.00f}; // 0.02 Angular velocity threshold filtering (rad/s)

    // Calibration parameters
    size_t bias_calibration_samples{300}; // Zero-bias calibration sample count
    float bias_threshold{0.05f};          // Zero-bias threshold (rad/s)

    // Closed-loop control parameters
    float rotation_tolerance{0.17f}; // Rotation completion tolerance (rad, about 10 degrees)
    float max_rotation_time{30.0f};  // Maximum rotation time (seconds)
    float correction_gain{0.5f};     // 0.3 Trajectory correction gain coefficient

    // Timeout backup parameters
    float backup_distance{0.3f}; // Backup distance (meters)
    float backup_speed{0.2f};    // Backup speed (meters/second)
    int max_backup_attempts{5};  // Maximum backup attempts

    // Data logging parameters
    bool enable_data_logging{false};                         // Enable data logging
    std::string log_file_path{"/userdata/log/imu_data.log"}; // Log file path
};

/**
 * @brief Rotation Control Result
 */
struct RotationControlResult
{
    bool completed{false};       // Whether rotation is completed
    bool timeout{false};         // Whether timeout occurred
    float actual_rotation{0.0f}; // Actual rotation angle (rad)
    float target_rotation{0.0f}; // Target rotation angle (rad)
    float rotation_error{0.0f};  // Rotation error (rad)
    float elapsed_time{0.0f};    // Elapsed time (seconds)
};

/**
 * @brief Linear Motion Control Result
 */
struct LinearMotionControlResult
{
    bool completed{false};       // Whether motion is completed
    bool timeout{false};         // Whether timeout occurred
    float actual_distance{0.0f}; // Actual moved distance (m)
    float target_distance{0.0f}; // Target moved distance (m)
    float distance_error{0.0f};  // Distance error (m)
    float heading_error{0.0f};   // Heading error (rad)
    float elapsed_time{0.0f};    // Elapsed time (seconds)
};

/**
 * @brief IMU Data Processor Class
 *
 * Features:
 * 1. IMU data subscription and preprocessing
 * 2. Zero-bias calibration and filtering
 * 3. Closed-loop rotation control based on IMU
 * 4. Data logging
 */
class ImuDataProcessor
{
public:
    explicit ImuDataProcessor(const ImuProcessorParam &param = ImuProcessorParam{});
    ~ImuDataProcessor();

    // Initialization and shutdown
    void Initialize();
    void Shutdown();

    void SetImuData(const ImuData &imu_data);
    void SetMotorSpeedData(const float &act_linear, const float &act_angular);

    // Set callback function to pass processed IMU data to the main algorithm class
    void SetImuDataCallback(std::function<void(const ImuData &)> callback);

    // Set velocity publish callback function, used for backup operation in rotation control
    void SetVelocityCallback(std::function<void(float, float, uint64_t)> callback);

    // Closed-loop rotation control interface
    RotationControlResult StartRotationControl(float target_angle, float angular_velocity);
    void StopRotationControl();
    bool IsRotationControlActive() const;

    // Closed-loop linear motion control interface
    LinearMotionControlResult StartLinearMotionControl(float target_distance, float expect_velocity, float target_heading = 0.0f);
    void StopLinearMotionControl();
    bool IsLinearMotionControlActive() const;

    // Continuous linear motion control interface (for continuous straight motion)
    void StartContinuousLinearMotion(float linear_velocity, float target_heading = 0.0f);
    void StopContinuousLinearMotion();
    bool IsContinuousLinearMotionActive() const;

    // Get current status
    bool IsBiasCalibrated() const;
    float getCurrentYaw() const;
    float getBiasZ() const;

    // Get position estimation
    float getEstimatedX() const;
    float getEstimatedY() const;

    // Parameter setting
    void SetParam(const ImuProcessorParam &param);
    ImuProcessorParam GetParam() const;

    // Reset state
    void ResetState();

private:
    // Core processing functions
    void ProcessImuData(const ImuData &imu_data);
    void CalibrateImuBias(const ImuData &imu_data);
    float ApplyLowPassFilter(float new_value, float &filtered_value, float alpha);
    void InitializeFilters(float initial_angular_velocity);

    // Angle processing function (left-handed coordinate system)
    float NormalizeAngle(float angle);

    // Trajectory deviation detection and correction
    bool IsTrajectoryDeviated(float heading_error, float max_deviation = 0.174f); // 10 degrees default threshold
    float CalculateTrajectoryCorrection(float heading_error, float max_correction = 0.5f);

    // Position estimation (based on IMU integration)
    void UpdatePositionEstimate(float linear_velocity, float angular_velocity, float dt);
    void ResetPositionEstimate();

    // Closed-loop rotation control
    void RotationControlThread();
    void UpdateRotationControl(float angular_velocity, float dt);

    // Closed-loop linear motion control
    void LinearMotionControlThread();
    void UpdateLinearMotionControl(float angular_velocity, float dt);

    // Continuous linear motion control
    void ContinuousLinearMotionThread();
    void UpdateContinuousLinearMotion(float angular_velocity, float dt);

    // Data logging
    void InitializeDataLogging();
    void LogFilteringData(uint64_t timestamp, float raw_angular_velocity, float filtered_angular_velocity);
    void CloseDataLogging();

private:
    // Parameters
    ImuProcessorParam param_;

    // Thread control
    std::atomic_bool processing_active_{false};
    std::atomic_bool rotation_control_active_{false};
    std::thread rotation_control_thread_;
    std::atomic_bool linear_motion_control_active_{false};
    std::thread linear_motion_control_thread_;
    std::atomic_bool continuous_linear_motion_active_{false};
    std::thread continuous_linear_motion_thread_;

    // Data mutexes
    std::mutex imu_data_mutex_;
    std::mutex rotation_control_mutex_;
    std::mutex linear_motion_control_mutex_;
    std::mutex continuous_linear_motion_mutex_;
    std::mutex motor_speed_mutex_;

    // Callback functions
    std::function<void(const ImuData &)> imu_data_callback_;
    std::function<void(float, float, uint64_t)> velocity_callback_; // Velocity publish callback (linear, angular, duration_ms)

    // Filtering and calibration status
    bool is_bias_calibrated_{false};
    bool filter_initialized_{false};
    float bias_z_{0.0f};
    std::vector<float> bias_samples_;
    float filtered_angular_velocity_{0.0f};

    // Time management
    std::chrono::steady_clock::time_point last_imu_time_;
    bool is_first_imu_{true};
    uint64_t last_imu_timestamp_{0};

    // Closed-loop rotation control status
    RotationControlResult rotation_result_;
    float target_rotation_angle_{0.0f};
    float accumulated_rotation_{0.0f};
    float target_angular_velocity_{0.0f};
    std::chrono::steady_clock::time_point rotation_start_time_;

    // Timeout backup status
    int backup_attempt_count_{0};                                       // Current backup attempt count
    bool is_backing_up_{false};                                         // Whether backing up
    std::chrono::steady_clock::time_point backup_start_time_;           // Backup start time
    std::chrono::steady_clock::time_point current_rotation_start_time_; // Current rotation start time

    // Closed-loop linear motion control status
    LinearMotionControlResult linear_motion_result_;
    float target_distance_{0.0f};                                    // Target distance (m)
    float target_heading_{0.0f};                                     // Target heading angle (rad)
    float accumulated_distance_{0.0f};                               // Accumulated moved distance (m)
    float linear_motion_accumulated_rotation_{0.0f};                 // Accumulated rotation angle (rad)
    float expect_linear_velocity_{0.0f};                             // Target linear velocity (m/s)
    float initial_heading_{0.0f};                                    // Initial heading angle (rad)
    std::chrono::steady_clock::time_point linear_motion_start_time_; // Linear motion start time

    // Continuous linear motion control status
    float continuous_target_heading_{0.0f};         // Continuous motion target heading angle (rad)
    float continuous_expect_linear_velocity_{0.0f}; // Continuous motion linear velocity (m/s)
    float continuous_linear_motion_rotation_{0.0f}; // Continuous motion accumulated rotation angle (rad)

    // Current status
    float current_yaw_{0.0f};

    // Position estimation status (left-handed coordinate system)
    float estimated_x_{0.0f}; // X position estimation
    float estimated_y_{0.0f}; // Y position estimation

    // Data logging
    std::ofstream log_file_;
    bool logging_initialized_{false};

    // Motor speed
    float act_linear_{0.0f};
    float act_angular_{0.0f};
};

} // namespace fescue_iox

#endif // IMU_DATA_PROCESSOR_HPP
