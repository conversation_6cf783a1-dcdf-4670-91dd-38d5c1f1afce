#ifndef NAVIGATION_RANDOM_MOWER_HPP
#define NAVIGATION_RANDOM_MOWER_HPP

#include "data_type.hpp"
#include "mower_msgs/msg/soc_exception.hpp"
#include "mower_msgs/msg/soc_imu.hpp"
#include "mower_msgs/msg/mcu_motor_speed.hpp"
#include "ob_mower_msgs/nav_random_mower_state__struct.h"
#include "obstacle_detector.hpp"
#include "velocity_publisher.hpp"

#include <atomic>
#include <chrono>
#include <cmath>
#include <cstdint>
#include <memory>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

struct RandomMowerAlgParam
{
    float dead_zone;
};

struct RandomMowerAlgResult
{
    float linear_vel;
    float angular_vel;
};

struct DataTimeInfo
{
    // 接收到的时间，微秒
    uint64_t recv_timestamp = 0;
    // 发送的时间，微秒
    uint64_t send_timestamp = 0;
    // 是否低频
    bool is_low_freq = false;
    uint32_t low_freq_count = 0;
    // 是否超时
    bool is_timeout = false;
};

class NavigationRandomMowerAlg
{
    enum class MotionState
    {
        NO_MOTION = 0,
        TRAP_PREPARE = 1,
        TRAP_BACK = 2,
        TRAP_ROTATE = 3,
        TRAP_FORWARD = 4,
        NORMAL_SLOWDOWN = 5,
        NORMAL_BACK = 6,
        NORMAL_ROTATE = 7,
        NORMAL_FORWARD = 8,
    };
    using iox_exception_publisher = iox::popo::Publisher<mower_msgs::msg::SocException>;

public:
    NavigationRandomMowerAlg(const RandomMowerAlgParam &param);
    ~NavigationRandomMowerAlg();
    void Run(const PerceptionFusionResult &fusion_result);
    void ResetRandomMowerFlags();
    void ProhibitVelPublisher();
    void SetRandomMowerAlgParam(const RandomMowerAlgParam &param);
    void SetRandomMowerStateCallback(std::function<void(RandomMowerRunningState state)> callback);
    void SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback);
    void SetVelPublisherProhibit(bool prohibit)
    {
        if (vel_publisher_)
        {
            vel_publisher_->SetProhibitFlag(prohibit);
        }
    }
    void SetAlgoRunningState(MowerRunningState state);
    void SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose);
    void SetOccupancyResult(const OccupancyResult &occupancy_result, uint64_t output_timestamp);
    void SetSocImu(const mower_msgs::msg::SocImu &data);
    void SetMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data);
    void SetBowOrRandomMower(const bool &is_bow_mower);// true is bow mower, false is random mower
    const char *GetVersion();
    void PrepareRecoverFromException();
    bool IsBowMower() 
    {
        return m_is_bow_mower_.load();
    }

private:
    void ProcessAvoidObstacleRotateSingleWheel(const double wheel_v);
    void ProcessAvoidObstacleRotateSingleWheel(const std::vector<std::vector<uint8_t>>& grid, int height, int width, int y_slow);
    void ProcessAvoidObstacleRotateBowMower(const std::vector<std::vector<uint8_t>>& grid, int height, int width, const ob_mower_msgs::NavFusionPose &current_pose);
    void RotateCurrentToStart(const ob_mower_msgs::NavFusionPose &current_pose, const bool failed);
    void ProcessTrapAvoidObstacleRotate(const std::vector<std::vector<uint8_t>>& grid, int height, int width);
    void ProcessAvoidObstacleFlags();
    void ResetAvoidObstacleFlags();
    void ResetTrapAvoidObstacleFlags();
    void ProcessAvoidObstacleSlowdown();
    void ProcessAvoidObstacleSlowdown(int height, int y_obs, float res);
    double calSlowVel(int height, int y_obs, float res, bool is_need_slow);

    void PublishVelocity(float linear, float angular, uint64_t duration_ms = 0);
    void PublishZeroVelocity();
    void PublishRandomMowerState(RandomMowerRunningState state);
    void PauseVelocity();
    void ResumeVelocity();
    void check_trap(ObstacleDetectionResult result);
    void InitPublisher();
    void PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value);
    // add edge follow
    void EdgeFollowDisable();
    void EdgeFollowEnable();
    void DealFeatureSelect(ThreadControl control, bool state);
    void AdjustHeading(float turning_angle_offset);
    void SetRotateState(bool is_trap);
    void SetBackState(bool is_trap);
    void UpdateBackState(bool is_trap);
    void UpdateNormalRotateState(const std::vector<std::vector<uint8_t>>& grid, int height, int width);
    void SetNormalForwardState();
    void UpdateTrapRotateVelocity(const std::vector<std::vector<uint8_t>>& grid, int height, int width);
    void UpdateRandomMotionState(const ObstacleDetectionResult& result, 
                                 const std::vector<std::vector<uint8_t>>& grid, 
                                 int height, int width, float resolution);
    bool CheckSocImuDataError();
    bool CheckMotorSpeedDataError();
    bool CheckBevDataError(const PerceptionFusionResult &fusion_result);
    void CheckTimeout(const std::string &name, const DataTimeInfo& last_time_info, 
                      uint64_t timeout, DataTimeInfo& cur_time_info);
    void UpdateDataTimeInfo(const std::string &name, const DataTimeInfo& last_time_info, 
                            uint64_t low_freq_time, uint32_t low_freq_count_max,
                            uint64_t cur_send_timestamp, DataTimeInfo& cur_time_info);

private:
    std::unique_ptr<VelocityPublisher> vel_publisher_{nullptr};
    std::unique_ptr<iox_exception_publisher> pub_exception_;
    std::function<void(RandomMowerRunningState state)> random_mower_state_callback_;
    std::function<void(const std::vector<FeatureSelectData> &)> feature_select_callback_;

    // 运行状态量
    MowerRunningState mower_running_state_{MowerRunningState::STOP}; // 0-未开始，1-运行中，2-暂停
    RandomMowerRunningState random_mower_running_state_{RandomMowerRunningState::UNDEFINED};
    bool have_obs_in_grass_ = false;
    bool is_surrounded_ = false;
    bool reach_edge_ = false;

    bool avoid_obstacle_reverse_complete_ = false;
    float perception_ctrl_reverse_distance_ = 0.2;
    bool avoid_obstacle_rotate_complete_ = false;
    float turning_angle_min_{0.523}; // 2.1
    float turning_angle_max_{2.1};   // 3.14//1.046
    uint64_t avoid_obstacle_rotate_duration_ = 0;
    uint64_t avoid_obstacle_rotate_start_time_ = 0;
    bool avoid_obstacle_ = false;

    bool avoid_obstacle_slowdown_complete_ = false;
    bool avoid_obstacle_slowdown_start_ = false;
    bool avoid_obstacle_rotate_start_ = false;
    float avoid_obstacle_slowdown_vel_linear_ = 0.0;
    float slow_down_time_gap_{0.01}; // 减速过程时间

    // 随机割草
    double random_v_{0.3};
    float start_slow_down_dis_{0.5};
    float stop_slow_down_dis_{0.3};
    double reverse_v_{0.1};
    double reverse_dis_{0.15}; // 0.5
    double wheel_base_{0.4};
    double wheel_v_{0.2};
    float danger_dis_{0.3};
    int find_target_{4};
    double last_v_{0};
    double last_w_{0};
    float rotation_w_{0};
    float spot_w_{0.5};
    float dead_zone_{0};
    int left_line_x_{11};
    int right_line_x_{28};
    std::unique_ptr<ObstacleDetector> obstacle_detector_{nullptr};
    bool is_save_data_{false};
    std::string file_path_{"/userdata/"};
    bool is_trap_{false};
    int is_danger_num_{0};
    int danger_threshold_{2};
    int straight_num_{0};
    int straight_threshold_{1000};
    bool mark_trap_timestamp_{false};
    std::chrono::steady_clock::time_point trap_timestamp_;
    double max_trap_duration_{360.0};
    float trap_spot_w_{0.3};
    bool trap_rotate_start_{false};
    float trap_rotate_angle_{0};
    std::chrono::steady_clock::time_point trap_rotate_timestamp_;
    int trap_forward_num_{0};
    int trap_forward_threshold_{188};
    float trap_v_{0.1};
    float trap_reverse_dis_{0.1};
    float trap_turning_angle_min_{2.6};
    float trap_turning_angle_max_{3.14};
    std::chrono::steady_clock::time_point trap_edge_follow_timestamp_;
    double max_trap_edge_follow_duration_{120.0};
    double wait_for_bias_time_{2.5};
    bool bias_first_stop_complete_{false};
    double m_random_forward_yaw_ = 0;

    std::atomic<bool> m_is_bow_mower_{false};
    int m_lateral_moving_fail_count_{0};
    int m_rotate_sign_{1};
    std::mutex m_fusion_pose_mtx_;
    double m_start_yaw{0};
    double m_target_yaw{0};
    double m_bow_lateral_dis_{0.15};
    double m_bow_line_dis{INT_MAX};
    double m_at_least_rotate_angle_{25};
    double m_bow_min_spd_{0.1};
    double m_bow_angular_p_{2.0};
    std::mutex m_expection_mtx_;
    bool m_judge_the_line_{false};
    int m_lateral_move_fail_conut_{0};
    int m_straight_move_fail_count_{0};
    ob_mower_msgs::NavFusionPose m_bow_start_pose_;
    ob_mower_msgs::NavFusionPose m_newest_pose_;
    std::vector<Eigen::Vector2d> m_inflateModels;
    int m_bow_left_line_x_{3};
    int m_bow_right_line_x_{33};
    double m_grid_inflate_radius_{0.15};
    MotionState motion_state_{MotionState::NO_MOTION};
    uint64_t back_duration_ms_ = 0;
    uint64_t back_start_time_ms_ = 0;
    bool is_normal_rotate_inited_ = false;
    float rotate_angle_ = 0;
    bool is_normal_forward_inited_ = false;
    bool is_new_trap_rotate_ = false;
    float trap_rotate_velocity_ = 0;
    int m_lateral_pitch_over_count_ = 0;
    std::vector<ob_mower_msgs::NavFusionPose> m_lateral_move_poses;

    std::vector<std::vector<uint8_t>> inflateObstacles(const std::vector<std::vector<uint8_t>> &grid, 
        const double inflation_radius, const float resolution);
    bool getAverageLateralPitch(double &result);

    std::mutex data_time_info_map_mtx_;
    std::unordered_map<std::string, DataTimeInfo> data_time_info_map_;
};

} // namespace fescue_iox

#endif
