#include "random_mower.hpp"

#include "mower_sdk_version.h"
#include "nav_utils.hpp"
#include "process_fusion.hpp"
#include "random_mower_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/rate.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"
#include "yaml-cpp/yaml.h"

#include <algorithm>
#include <chrono>
#include <cmath>      // for std::labs()
#include <filesystem> //c++17
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

namespace fescue_iox
{

NavigationRandomMowerAlg::NavigationRandomMowerAlg(const RandomMowerAlgParam &param)
    : vel_publisher_(std::make_unique<VelocityPublisher>("RandomMower"))
{
    SetRandomMowerAlgParam(param);
    obstacle_detector_ = std::make_unique<ObstacleDetector>(dead_zone_, danger_dis_, start_slow_down_dis_);
    InitPublisher();
}

NavigationRandomMowerAlg::~NavigationRandomMowerAlg()
{
    PublishZeroVelocity();
    LOG_WARN("NavigationRandomMowerAlg exit!");
}

void NavigationRandomMowerAlg::SetAlgoRunningState(MowerRunningState state)
{
    LOG_INFO("NavigationRandomMowerAlg running state: {}", asStringLiteral(state));
    if (state == MowerRunningState::RUNNING)
    {
        {
            std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
            data_time_info_map_.clear();
            uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
            DataTimeInfo data_time_info;
            // 接收时间，初始化为当前时间
            data_time_info.recv_timestamp = now_timestamp;
            // 发送时间，初始化为0
            data_time_info.send_timestamp = 0;
            data_time_info.is_low_freq = false;
            data_time_info.low_freq_count = 0;
            data_time_info.is_timeout = false;
            data_time_info_map_["Bev"] = data_time_info;
            data_time_info_map_["SocImu"] = data_time_info;
            data_time_info_map_["MotorSpeed"] = data_time_info;
        }
        SetNormalForwardState();
        ResumeVelocity();
        is_new_trap_rotate_ = false;

        ob_mower_msgs::NavFusionPose current_pose;
        {
            std::lock_guard<std::mutex> lock(m_fusion_pose_mtx_);
            current_pose = m_newest_pose_;
        }
        if (m_is_bow_mower_.load())
        {
            m_start_yaw = current_pose.yaw;
            m_target_yaw = m_start_yaw;
            m_bow_line_dis = INT_MAX;
        }
        else
        {
            m_random_forward_yaw_ = current_pose.yaw;
        }
    }
    else if (state == MowerRunningState::PAUSE)
    {
        ResetRandomMowerFlags();
        PauseVelocity();
    }
    else
    {
        LOG_ERROR("[NavigationRandomMowerAlg] Unknown state {}!", asStringLiteral(state));
    }
    mower_running_state_ = state;
}

const char *NavigationRandomMowerAlg::GetVersion()
{
    return "V1.0.0";
}

bool NavigationRandomMowerAlg::CheckSocImuDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("SocImu");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 200000;
    auto last_time_info = iter->second;
    CheckTimeout("SocImu", last_time_info, timeout, data_time_info_map_["SocImu"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationRandomMowerAlg::CheckMotorSpeedDataError()
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MotorSpeed");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 200000;
    auto last_time_info = iter->second;
    CheckTimeout("MotorSpeed", last_time_info, timeout, data_time_info_map_["MotorSpeed"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

bool NavigationRandomMowerAlg::CheckBevDataError(const PerceptionFusionResult &fusion_result)
{
    // check by output time
    uint64_t now_timestamp = std::chrono::steady_clock::now().time_since_epoch().count();
    double bev_delta_time = (now_timestamp - fusion_result.output_timestamp * 1000000) * 0.000000001; // seconds
    if (bev_delta_time > 0.5)
    {
        LOG_ERROR_THROTTLE(1000, "Current mode = {} , Bev timeout, delta time = {}", m_is_bow_mower_.load() ? "bow" : "random", bev_delta_time);
        return true;
    }
    // check by recv time
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("Bev");
    if (iter == data_time_info_map_.end())
    {
        return false;
    }
    uint64_t timeout = 1000000;
    auto last_time_info = iter->second;
    CheckTimeout("Bev", last_time_info, timeout, data_time_info_map_["Bev"]);
    if (iter->second.is_timeout || iter->second.is_low_freq)
    {
        return true;
    }
    return false;
}

// make sure the dead zone is correct
#if 1

void NavigationRandomMowerAlg::SetFeatureSelectCallback(std::function<void(const std::vector<FeatureSelectData> &)> callback)
{
    feature_select_callback_ = callback;
}

void NavigationRandomMowerAlg::DealFeatureSelect(ThreadControl control, bool state)
{
    std::vector<FeatureSelectData> feature_data;
    feature_data.clear();
    FeatureSelectData feature{control, static_cast<int>(NavAlgCtrlState::IGNORE)};
    feature.alg_status = state ? static_cast<int>(NavAlgCtrlState::ENABLE) : static_cast<int>(NavAlgCtrlState::DISABLE);
    feature_data.push_back(feature);
    if (feature_select_callback_ && !feature_data.empty())
    {
        feature_select_callback_(feature_data);
    }
}

void NavigationRandomMowerAlg::EdgeFollowDisable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, false);
}

void NavigationRandomMowerAlg::EdgeFollowEnable()
{
    DealFeatureSelect(ThreadControl::PERCEPTION_EDGE_THREAD, true);
}

void NavigationRandomMowerAlg::UpdateRandomMotionState(const ObstacleDetectionResult &result,
                                                       const std::vector<std::vector<uint8_t>> &grid,
                                                       int height, int width, float resolution)
{
    if (!result.result_valid)
    {
        LOG_INFO_THROTTLE(20, "random mower no motion state, no valid result");
        motion_state_ = MotionState::NO_MOTION;
        PublishZeroVelocity();
        return;
    }
    bool is_trap_rotate_recently = false;
    if (motion_state_ == MotionState::TRAP_ROTATE)
    {
        std::chrono::duration<double> rotate_duration = std::chrono::steady_clock::now() - trap_rotate_timestamp_;
        double trap_rotate_time_min = 1.0;
        if (rotate_duration.count() < trap_rotate_time_min)
        {
            // 开始trap rotate后，需要旋转至少1秒
            is_trap_rotate_recently = true;
        }
    }
    if (motion_state_ == MotionState::TRAP_PREPARE)
    {
        LOG_INFO("random mower switch to trap back");
        SetBackState(true);
    }
    else if (motion_state_ == MotionState::TRAP_BACK)
    {
        UpdateBackState(true);
    }
    else if (motion_state_ == MotionState::NORMAL_SLOWDOWN)
    {
        ProcessAvoidObstacleSlowdown(height, result.y_in_forward, resolution);
        if (avoid_obstacle_slowdown_complete_)
        {
            LOG_INFO("random mower normal slowdown over, switch to back");
            SetBackState(false);
        }
    }
    else if (motion_state_ == MotionState::NORMAL_BACK)
    {
        UpdateBackState(false);
    }
    else if (motion_state_ == MotionState::NORMAL_ROTATE)
    {
        UpdateNormalRotateState(grid, height, width);
    }
    else if (is_trap_ && !result.is_danger && !result.is_slow && !is_trap_rotate_recently)
    {
        motion_state_ = MotionState::TRAP_FORWARD;
        ResetTrapAvoidObstacleFlags();
        PublishVelocity(trap_v_, 0);
        trap_forward_num_++;
        LOG_INFO_THROTTLE(2000, "Trap! try to go forward,num:{}", trap_forward_num_);
    }
    else if (motion_state_ == MotionState::TRAP_ROTATE)
    {
        ProcessTrapAvoidObstacleRotate(grid, height, width);
        if (avoid_obstacle_rotate_complete_)
        {
            LOG_INFO("random mower trap rotate over, switch to forward");
            ResetTrapAvoidObstacleFlags();
            ResetAvoidObstacleFlags();
            SetNormalForwardState();
        }
    }
    else if (is_trap_ && (result.is_danger || result.is_slow) && motion_state_ != MotionState::TRAP_PREPARE &&
             motion_state_ != MotionState::TRAP_BACK && motion_state_ != MotionState::TRAP_ROTATE)
    {
        LOG_INFO("random mower switch to trap prepare");
        motion_state_ = MotionState::TRAP_PREPARE;
        if (result.is_danger)
        {
            is_surrounded_ = trap_forward_num_ > trap_forward_threshold_ ? true : false;
        }
        else
        {
            is_surrounded_ = false;
        }
        trap_forward_num_ = 0;
        if (!avoid_obstacle_slowdown_complete_)
        {
            PublishZeroVelocity();
            avoid_obstacle_slowdown_complete_ = true;
        }
    }
    else
    {
        is_new_trap_rotate_ = false;
        if (result.is_danger && !reach_edge_)
        {
            LOG_INFO("random mower reach edge");
            reach_edge_ = true;
            is_surrounded_ = true;
        }
        else if (result.is_slow && !have_obs_in_grass_)
        {
            LOG_INFO("random mower have obs in grass");
            have_obs_in_grass_ = true;
        }
        if (reach_edge_)
        {
            avoid_obstacle_slowdown_complete_ = true;
            PublishZeroVelocity();
            LOG_INFO("random mower reach edge, switch to back");
            SetBackState(false);
        }
        else if (have_obs_in_grass_)
        {
            LOG_INFO("random mower have obs in grass, switch to slowdown");
            motion_state_ = MotionState::NORMAL_SLOWDOWN;
        }
        else
        {
            ob_mower_msgs::NavFusionPose current_pose;
            {
                std::lock_guard<std::mutex> lock(m_fusion_pose_mtx_);
                current_pose = m_newest_pose_;
            }
            if (!is_normal_forward_inited_)
            {
                m_random_forward_yaw_ = current_pose.yaw;
                is_normal_forward_inited_ = true;
            }
            double angular_vel = -NormalizeAngle(current_pose.yaw - m_random_forward_yaw_) * m_bow_angular_p_;
            double format_angular = NormalizeAngle(angular_vel);
            int sign = 1;
            if (format_angular < 0)
                sign = -1;
            if (fabs(format_angular) > 0.5)
                format_angular = 0.5 * sign;
            PublishVelocity(random_v_, format_angular);
            LOG_INFO("go straight current yaw = {}  random yaw = {}", current_pose.yaw, m_random_forward_yaw_);
        }
    }
}

void NavigationRandomMowerAlg::SetNormalForwardState()
{
    is_normal_forward_inited_ = false;
    motion_state_ = MotionState::NORMAL_FORWARD;
}

void NavigationRandomMowerAlg::PrepareRecoverFromException()
{
    LOG_INFO("prepare recover from exception");
    if (m_is_bow_mower_.load())
    {
        {
            std::lock_guard<std::mutex> lock(m_expection_mtx_);
            ResetAvoidObstacleFlags();
            reach_edge_ = true;
            m_lateral_move_fail_conut_ = 0;
            m_straight_move_fail_count_ = 0;
            m_lateral_moving_fail_count_ = 0;

            m_bow_line_dis = INT_MAX;
            ob_mower_msgs::NavFusionPose current_pose;
            {
                std::lock_guard<std::mutex> lock(m_fusion_pose_mtx_);
                current_pose = m_newest_pose_;
            }
            m_bow_start_pose_ = current_pose;
            double random = GenerateRandomNumber<double>(turning_angle_min_, turning_angle_max_);
            m_start_yaw = NormalizeAngle(m_start_yaw + random);
            m_rotate_sign_ *= -1;
        }
    }
    else
    {
        reach_edge_ = true;
    }
}

void NavigationRandomMowerAlg::CheckTimeout(const std::string &name, const DataTimeInfo &last_time_info,
                                            uint64_t timeout, DataTimeInfo &cur_time_info)
{
    uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
    // check timeout
    uint64_t delta_recv_time = now_timestamp - last_time_info.recv_timestamp;
    cur_time_info.is_timeout = false;
    if (delta_recv_time > timeout)
    {
        LOG_ERROR_THROTTLE(2000, "{} timeout, delta_recv_time: {} us total timeout: {} us", name, delta_recv_time, timeout);
        cur_time_info.is_timeout = true;
    }
}

void NavigationRandomMowerAlg::UpdateDataTimeInfo(const std::string &name, const DataTimeInfo &last_time_info,
                                                  uint64_t low_freq_time, uint32_t low_freq_count_max,
                                                  uint64_t cur_send_timestamp, DataTimeInfo &cur_time_info)
{
    uint64_t now_timestamp = GetSteadyClockTimestampMs() * 1000;
    // check low freq
    uint64_t delta_send_time = cur_send_timestamp - last_time_info.send_timestamp;
    if (last_time_info.send_timestamp > 0 && (delta_send_time == 0 || delta_send_time > low_freq_time))
    {
        cur_time_info.low_freq_count++;
    }
    else
    {
        cur_time_info.low_freq_count = 0;
    }
    cur_time_info.is_low_freq = false;
    if (cur_time_info.low_freq_count > low_freq_count_max)
    {
        LOG_ERROR("{} low freq, cur delta_send_time: {} us", name, delta_send_time);
        cur_time_info.is_low_freq = true;
    }
    cur_time_info.recv_timestamp = now_timestamp;
    cur_time_info.send_timestamp = cur_send_timestamp;
}

void NavigationRandomMowerAlg::SetSocImu(const mower_msgs::msg::SocImu &data)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("SocImu");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 100000;
    uint32_t low_freq_count_max = 30;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("SocImu", last_time_info, low_freq_time, low_freq_count_max, data.system_timestamp, data_time_info_map_["SocImu"]);
}

void NavigationRandomMowerAlg::SetMcuMotorSpeed(const mower_msgs::msg::McuMotorSpeed &data)
{
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("MotorSpeed");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 100000;
    uint32_t low_freq_count_max = 30;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("MotorSpeed", last_time_info, low_freq_time, low_freq_count_max, data.system_timestamp, data_time_info_map_["MotorSpeed"]);
}

void NavigationRandomMowerAlg::SetRotateState(bool is_trap)
{
    if (is_trap)
    {
        LOG_INFO("random mower set trap rotate state");
        motion_state_ = MotionState::TRAP_ROTATE;
        trap_rotate_timestamp_ = std::chrono::steady_clock::now();
    }
    else
    {
        LOG_INFO("random mower set normal rotate state");
        motion_state_ = MotionState::NORMAL_ROTATE;
    }
    avoid_obstacle_rotate_complete_ = false;
    trap_rotate_start_ = false;
    is_normal_rotate_inited_ = false;
}

void NavigationRandomMowerAlg::SetBackState(bool is_trap)
{
    if (is_trap)
    {
        LOG_INFO("random mower set trap back state");
        motion_state_ = MotionState::TRAP_BACK;
    }
    else
    {
        LOG_INFO("random mower set normal back state");
        motion_state_ = MotionState::NORMAL_BACK;
    }
    avoid_obstacle_reverse_complete_ = false;
    back_start_time_ms_ = 0;
    back_duration_ms_ = 0;
}

void NavigationRandomMowerAlg::UpdateBackState(bool is_trap)
{
    if (!avoid_obstacle_slowdown_complete_ || !is_surrounded_)
    {
        // 直接跳转
        LOG_INFO("random mower switch to trap rotate directly slowdown {} surrounded {}", avoid_obstacle_slowdown_complete_, is_surrounded_);
        SetRotateState(is_trap);
    }
    else
    {
        if (back_start_time_ms_ == 0)
        {
            back_start_time_ms_ = GetSteadyClockTimestampMs();
            back_duration_ms_ = CalculateDuration(trap_reverse_dis_, reverse_v_);
            LOG_INFO("random mower back duration: {}", back_duration_ms_);
        }
        auto time_now = GetSteadyClockTimestampMs();
        if (time_now - back_start_time_ms_ > back_duration_ms_)
        {
            LOG_INFO("random mower back over, switch to rotate");
            avoid_obstacle_reverse_complete_ = true;
            SetRotateState(is_trap);
        }
        else
        {
            PublishVelocity(-reverse_v_, 0);
        }
    }
}

void NavigationRandomMowerAlg::UpdateNormalRotateState(const std::vector<std::vector<uint8_t>> &grid, int height, int width)
{
    if (!is_normal_rotate_inited_)
    {
        int obs_num_left = 0;
        int obs_num_right = 0;
        for (int y = height - 1; y >= 0; y--)
        {
            for (int l_x = 0; l_x <= left_line_x_; l_x++)
            {
                if (grid[y][l_x] == 1)
                {
                    obs_num_left++;
                }
            }
            for (int r_x = right_line_x_; r_x < width; r_x++)
            {
                if (grid[y][r_x] == 1)
                {
                    obs_num_right++;
                }
            }
        }
        if (obs_num_left <= obs_num_right)
        {
            rotation_w_ = spot_w_;
        }
        else
        {
            rotation_w_ = -spot_w_;
        }
        float rotate_angle = GenerateRandomNumber<float>(turning_angle_min_, turning_angle_max_); // 计算转弯随机角度
        rotate_angle_ = rotate_angle;
        float signed_rotate_angle = fabs(rotate_angle);
        if (rotation_w_ < 0)
        {
            signed_rotate_angle = -signed_rotate_angle;
        }
        AdjustHeading(signed_rotate_angle);
        is_normal_rotate_inited_ = true;
    }
    if (vel_publisher_->IsExecutionCompleted())
    {
        LOG_INFO("random mower normal rotate over, switch to forward");
        SetNormalForwardState();
        ResetAvoidObstacleFlags();
        avoid_obstacle_rotate_complete_ = true;
        if (is_save_data_)
        {
            std::ofstream datafile(file_path_ + "rotate_angle.txt", std::ofstream::app);
            if (!datafile.is_open())
            {
                LOG_ERROR_THROTTLE(1000, "can not open file rotate_angle.txt!!!!");
            }
            else
            {
                datafile << std::fixed << rotate_angle_ << ", " << rotate_angle_ / M_PI * 180 << std::endl;
            }
            datafile.close();
        }
    }
}

void NavigationRandomMowerAlg::Run(const PerceptionFusionResult &fusion_result)
{
    if (mower_running_state_ == MowerRunningState::PAUSE)
    {
        LOG_WARN_THROTTLE(3000, "Random mower Run() is PAUSE!");
        return;
    }

    LOG_WARN_THROTTLE(3000, "NavigationRandomMowerAlg Run ................Current mode = {}................", m_is_bow_mower_.load() ? "bow" : "random");

    if (!obstacle_detector_)
    {
        LOG_ERROR_THROTTLE(1000, "obstacle_detector is nullptr!");
        return;
    }

    if (CheckBevDataError(fusion_result))
    {
        LOG_ERROR_THROTTLE(1000, "Bev timeout! Current mode = {}", m_is_bow_mower_.load() ? "bow" : "random");
        PublishZeroVelocity();
        return;
    }
    if (CheckSocImuDataError())
    {
        LOG_ERROR_THROTTLE(1000, "SocImu timeout! Current mode = {}", m_is_bow_mower_.load() ? "bow" : "random");
        PublishZeroVelocity();
        return;
    }
    if (CheckMotorSpeedDataError())
    {
        LOG_ERROR_THROTTLE(1000, "MotorSpeed timeout! Current mode = {}", m_is_bow_mower_.load() ? "bow" : "random");
        PublishZeroVelocity();
        return;
    }

    auto start = std::chrono::steady_clock::now();
    float resolution = fusion_result.occupancy_grid.resolution;
    const std::vector<std::vector<uint8_t>> &grid = fusion_result.occupancy_grid.grid;

    int height = fusion_result.occupancy_grid.height;
    int width = fusion_result.occupancy_grid.width;

    ObstacleDetectionResult result;
    result = obstacle_detector_->DetectObstaclesForBow(grid, height, width, resolution, 0);

    if (m_is_bow_mower_.load())
    {
        {
            std::lock_guard<std::mutex> lock(m_expection_mtx_);
            // bow mower
            ob_mower_msgs::NavFusionPose current_pose;
            {
                std::lock_guard<std::mutex> lock(m_fusion_pose_mtx_);
                current_pose = m_newest_pose_;
            }

            if (result.is_danger && !reach_edge_)
            {
                reach_edge_ = true;
                is_surrounded_ = true;
            }
            double dis = hypot(m_bow_start_pose_.x - current_pose.x,
                m_bow_start_pose_.y - current_pose.y);
            if (reach_edge_)
            {
                if(!m_judge_the_line_){
                    m_judge_the_line_ = true;
                    if(m_bow_line_dis < INT_MAX * 0.5){
                        //lateral
                        if(dis < m_bow_line_dis * 0.8f){
                            m_lateral_move_fail_conut_++;
                        }else {
                            m_lateral_move_fail_conut_--;
                        }
                    }else{
                        //straight
                        if(dis < 0.5){
                            m_straight_move_fail_count_++;
                        }else {
                            m_straight_move_fail_count_--;
                        }
                    }
                }
                if(m_lateral_move_fail_conut_ > 1 || m_straight_move_fail_count_ > 5){
                    m_lateral_move_fail_conut_ = 0;
                    m_straight_move_fail_count_ = 0;
                    m_lateral_moving_fail_count_ = 0;

                    m_bow_line_dis = INT_MAX;
                    m_bow_start_pose_ = current_pose;
                    double random = GenerateRandomNumber<double>(turning_angle_min_, turning_angle_max_);
                    m_start_yaw = NormalizeAngle(m_start_yaw + random);
                    m_rotate_sign_ *= -1;
                }
                LOG_DEBUG_THROTTLE(1000, "reach edge!");
                avoid_obstacle_slowdown_complete_ = true;
                if (!avoid_obstacle_reverse_complete_)
                {
                    PublishZeroVelocity();
                }
                if (fabs(current_pose.linear_velocity) < 0.05 && fabs(current_pose.angular_velocity) < 0.05)
                {
                    avoid_obstacle_reverse_complete_ = true;
                }
                double delta_yaw = fabs(NormalizeAngle(m_target_yaw - m_start_yaw));
                // cout << "delta_yaw = " << delta_yaw << endl;
                if (delta_yaw < 0.0174)
                {
                    ProcessAvoidObstacleRotateBowMower(grid, height, width, current_pose);
                }
                else
                {
                    // cout << "rotate to start" << endl;
                    RotateCurrentToStart(current_pose, true);
                }
                ProcessAvoidObstacleFlags();
            }
            else
            {
                m_judge_the_line_ = false;
                // cout << "dis = " << dis << " m_bow_line_dis = " << m_bow_line_dis << endl;
                m_lateral_move_poses.emplace_back(current_pose);
                if (dis > m_bow_line_dis)
                {
                    double aver_pitch = 0;
                    if (getAverageLateralPitch(aver_pitch))
                    {
                        if (fabs(aver_pitch) >= 0.0174 * 10)
                        {
                            m_lateral_pitch_over_count_++;
                        }
                        else
                        {
                            m_lateral_pitch_over_count_--;
                            if (m_lateral_pitch_over_count_ < 0)
                                m_lateral_pitch_over_count_ = 0;
                        }
                        if (m_lateral_pitch_over_count_ > 6)
                        {
                            // on slope, change the target yaw
                            m_lateral_move_poses.clear();
                            m_lateral_pitch_over_count_ = 0;
                            m_start_yaw = current_pose.yaw;
                            m_target_yaw = m_start_yaw;
                            return;
                        }
                    }
                    RotateCurrentToStart(current_pose, false);
                }
                else
                {
                    double linear_vel = calSlowVel(height, result.y_in_forward, resolution, result.is_slow);
                    double angular_vel = -NormalizeAngle(current_pose.yaw - m_target_yaw) * m_bow_angular_p_;
                    double format_angular = NormalizeAngle(angular_vel);
                    int sign = 1;
                    if (format_angular < 0)
                        sign = -1;
                    if (fabs(format_angular) > 0.5)
                        format_angular = 0.5 * sign;
                    PublishVelocity(linear_vel, format_angular);

                    LOG_INFO("go straight current yaw = {}  target yaw = {}", current_pose.yaw, m_target_yaw);
                }
            }
            return;
            //======bow end=========
        }
    }

    check_trap(result);
    LOG_INFO_THROTTLE(1000, "-----is trap {}", is_trap_);
    UpdateRandomMotionState(result, grid, height, width, resolution);

    auto end = std::chrono::steady_clock::now();
    std::chrono::duration<double> duration = end - start;
    LOG_DEBUG_THROTTLE(10000, "new random mower cost time: {} seconds", duration.count());
}
#endif

void NavigationRandomMowerAlg::SetBowOrRandomMower(const bool &is_bow_mower)
{
    m_is_bow_mower_.store(is_bow_mower);
    ob_mower_msgs::NavFusionPose current_pose;
    {
        std::lock_guard<std::mutex> lock(m_fusion_pose_mtx_);
        current_pose = m_newest_pose_;
    }
    if (m_is_bow_mower_.load())
    {
        m_start_yaw = current_pose.yaw;
        m_target_yaw = m_start_yaw;
        m_bow_line_dis = INT_MAX;
    }
    else
    {
        m_random_forward_yaw_ = current_pose.yaw;
    }
}

bool NavigationRandomMowerAlg::getAverageLateralPitch(double &result)
{
    if (m_lateral_move_poses.size() > 2)
        return false;
    double sumSin = 0.0;
    double sumCos = 0.0;
    for (auto pose : m_lateral_move_poses)
    {
        sumSin += std::sin(pose.pitch);
        sumCos += std::cos(pose.pitch);
    }
    result = std::atan2(sumSin / m_lateral_move_poses.size(), sumCos / m_lateral_move_poses.size());
    return true;
}

std::vector<std::vector<uint8_t>> NavigationRandomMowerAlg::inflateObstacles(const std::vector<std::vector<uchar>> &grid,
                                                                             const double inflation_radius, const float resolution)
{
    if (m_inflateModels.empty())
    {
        int inflate_pixel_dis = inflation_radius / resolution + 0.5f;
        for (int y = -inflate_pixel_dis; y < inflate_pixel_dis; y++)
        {
            for (int x = -inflate_pixel_dis; x < inflate_pixel_dis; x++)
            {
                double dis = std::hypot(x, y);
                if (dis < inflate_pixel_dis)
                    m_inflateModels.emplace_back(Eigen::Vector2d(x, y));
            }
        }
    }

    const int rows = grid.size();
    const int cols = grid[0].size();
    std::vector<std::vector<uint8_t>> results(rows, std::vector<uint8_t>(cols, 0));
    for (int y = 0; y < rows; y++)
    {
        for (int x = 0; x < cols; x++)
        {
            if (grid[y][x] == 1)
            {
                for (auto p : m_inflateModels)
                {
                    int xx = p.x() + x;
                    int yy = p.y() + y;
                    if (xx >= 0 && xx < cols && yy >= 0 && yy < rows)
                    {
                        results[yy][xx] = 1;
                    }
                }
            }
        }
    }

    return results;
}

double NavigationRandomMowerAlg::calSlowVel(int height, int y_obs, float res, bool is_need_slow)
{
    if (!is_need_slow)
        return random_v_;
    float obs_dis = (height - y_obs) * res + dead_zone_;
    if (obs_dis <= stop_slow_down_dis_ - 0.05)
    {
        return m_bow_min_spd_;
    }
    else
    {
        if (obs_dis > start_slow_down_dis_)
        {
            return m_bow_min_spd_;
        }
        else // normally obs_dis should be less than  start_slow_down_dis_
        {
            float vel_linear = random_v_ * (obs_dis - stop_slow_down_dis_) / (start_slow_down_dis_ - stop_slow_down_dis_);
            if (vel_linear < m_bow_min_spd_)
                vel_linear = m_bow_min_spd_;
            return vel_linear;
        }
    }
}

void NavigationRandomMowerAlg::RotateCurrentToStart(const ob_mower_msgs::NavFusionPose &current_pose, const bool failed)
{
    m_lateral_move_poses.clear();
    double current_yaw = current_pose.yaw;
    double delta_yaw = (NormalizeAngle(current_yaw - m_start_yaw));
    LOG_INFO("RotateCurrentToStart current yaw = {}  start yaw = {}   sign = {}  delta yaw = {}", current_pose.yaw, m_start_yaw, m_rotate_sign_, delta_yaw);
    if ((delta_yaw * m_rotate_sign_) >= 0)
    {
        PublishZeroVelocity();
        if (fabs(current_pose.linear_velocity) < 0.05 && fabs(current_pose.angular_velocity) < 0.05)
        {
            m_target_yaw = m_start_yaw;
            m_bow_start_pose_ = current_pose;
            m_bow_line_dis = INT_MAX;
            m_rotate_sign_ *= -1;
            avoid_obstacle_rotate_complete_ = true;
            if (failed)
                m_lateral_moving_fail_count_++;
        }
    }
    else
    {
        double real_w_ = 2.0 * fabs(delta_yaw);
        if (real_w_ > spot_w_ - 0.01)
            real_w_ = spot_w_ - 0.01;
        if (real_w_ < 0.1)
            real_w_ = 0.1;
        // int sign = -1;
        // if (delta_yaw < 0)
        //     sign = 1;
        PublishVelocity(0, real_w_ * m_rotate_sign_);
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleRotateBowMower(const std::vector<std::vector<uint8_t>> &grid, int height, int width,
                                                                  const ob_mower_msgs::NavFusionPose &current_pose)
{
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        !avoid_obstacle_rotate_complete_)
    {
        // double at_least_yaw = NormalizeAngle(m_target_yaw + 0.0174 * 45 * m_rotate_sign_);//at least rotate 45 degree
        // double over_yaw = NormalizeAngle(m_target_yaw + 0.0174 * 135 * m_rotate_sign_);// rotate over 135 degree than turn around

        double delta_yaw = fabs(NormalizeAngle(current_pose.yaw - m_start_yaw));

        // at least rotate 45 degree
        if (delta_yaw < 0.0174 * m_at_least_rotate_angle_)
        {
            PublishVelocity(0, spot_w_ * m_rotate_sign_);
            avoid_obstacle_rotate_complete_ = false;
            return;
        }
        else if (delta_yaw > 0.0174 * (180 - m_at_least_rotate_angle_))
        {
            // rotate over 135 degree than turn around
            double next_target_yaw = NormalizeAngle(m_start_yaw + M_PI);
            double delta_to_end_yaw = (NormalizeAngle(current_pose.yaw - next_target_yaw));
            if (/*fabs(delta_to_end_yaw) < 0.0174*/delta_yaw > 0.0174 * (179.5))
            {
                // done turn around, need to change rotate sign
                PublishZeroVelocity();

                if (fabs(current_pose.linear_velocity) < 0.05 && fabs(current_pose.angular_velocity) < 0.05)
                {
                    avoid_obstacle_rotate_complete_ = true;
                    m_target_yaw = next_target_yaw;
                    m_start_yaw = next_target_yaw;
                    m_lateral_moving_fail_count_++;
                    m_bow_line_dis = INT_MAX;
                    m_bow_start_pose_ = current_pose;
                    if (m_lateral_moving_fail_count_ > 1)
                    {
                        // no need to change rotate sign
                        m_lateral_moving_fail_count_ = 0;
                        double random = GenerateRandomNumber<double>(turning_angle_min_, turning_angle_max_);
                        m_start_yaw = NormalizeAngle(m_start_yaw + random);
                    }
                    m_rotate_sign_ *= -1;
                }

                return;
            }
            else
            {
                double real_w_ = 2.0 * fabs(delta_to_end_yaw);
                if (real_w_ > spot_w_)
                    real_w_ = spot_w_;
                if (real_w_ < 0.1)
                    real_w_ = 0.1;
                // int sign = -1;
                // if (delta_to_end_yaw < 0)
                //     sign = 1;
                // PublishVelocity(0, real_w_ * sign);
                PublishVelocity(0, real_w_ * m_rotate_sign_);
                return;
            }
        }

        double forward_dis = fabs(m_bow_lateral_dis_ / cos(fabs(current_pose.yaw - m_start_yaw + M_PI * 0.5f)));
        int check_height = height - 1 - (forward_dis + danger_dis_ - dead_zone_) / 0.025;
        if (check_height < 0)
            check_height = 0;

        int obs_num_middle = 0;
        for (int y = height - 1; y >= check_height; y--)
        {
            if (m_rotate_sign_)
            {
                for (int l_x = m_bow_left_line_x_; l_x <= width; l_x++)
                {
                    // double dis = std::hypot(l_x - current_pose.x, y - current_pose.y);
                    if (grid[y][l_x] == 1)
                    {
                        obs_num_middle++;
                    }
                }
            }
            else
            {
                for (int l_x = 0; l_x <= m_bow_right_line_x_; l_x++)
                {
                    // double dis = std::hypot(l_x - current_pose.x, y - current_pose.y);
                    if (grid[y][l_x] == 1)
                    {
                        obs_num_middle++;
                    }
                }
            }
        }

        LOG_ERROR_THROTTLE(1000, "pose.yaw:{} , m_start_yaw:{} , delta_yaw:{} , obs_num_middle:{} , m_rotate_sign_:{}", current_pose.yaw, m_start_yaw, delta_yaw, obs_num_middle, m_rotate_sign_);

        if (obs_num_middle <= 0)
        {
            PublishZeroVelocity();
            if (fabs(current_pose.linear_velocity) < 0.05 && fabs(current_pose.angular_velocity) < 0.05)
            {
                avoid_obstacle_rotate_complete_ = true;
                m_lateral_moving_fail_count_ = 0;
                m_start_yaw = NormalizeAngle(m_start_yaw + M_PI);
                m_target_yaw = current_pose.yaw;
                m_bow_line_dis = forward_dis;
                m_bow_start_pose_ = current_pose;
            }
        }
        else
        {
            PublishVelocity(0, /*spot_w_*/ 0.3 * m_rotate_sign_);
            avoid_obstacle_rotate_complete_ = false;
        }
    }
}

void NavigationRandomMowerAlg::ResetRandomMowerFlags()
{
    avoid_obstacle_ = false;
    have_obs_in_grass_ = false;
    reach_edge_ = false;
    is_surrounded_ = false;
    avoid_obstacle_slowdown_start_ = false;
    avoid_obstacle_slowdown_complete_ = false;
    avoid_obstacle_reverse_complete_ = false;
    avoid_obstacle_rotate_complete_ = false;
    is_trap_ = false;
    bias_first_stop_complete_ = false;
    mark_trap_timestamp_ = false;
    is_danger_num_ = 0;
    trap_forward_num_ = 0;
    trap_rotate_start_ = false;

    m_lateral_move_fail_conut_ = 0;
    m_straight_move_fail_count_ = 0;
    m_lateral_moving_fail_count_ = 0;
    m_lateral_pitch_over_count_ = 0;
    m_lateral_move_poses.clear();
    m_judge_the_line_ = false;

}

void NavigationRandomMowerAlg::SetRandomMowerAlgParam(const RandomMowerAlgParam &param)
{
    dead_zone_ = param.dead_zone;
}

void NavigationRandomMowerAlg::SetRandomMowerStateCallback(std::function<void(RandomMowerRunningState state)> callback)
{
    random_mower_state_callback_ = callback;
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleRotateSingleWheel(const double wheel_v)
{
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        !avoid_obstacle_rotate_complete_)
    {
        float rotate_angle = GenerateRandomNumber<float>(turning_angle_min_, turning_angle_max_);              // 计算转弯随机角度
        avoid_obstacle_rotate_duration_ = static_cast<uint64_t>(rotate_angle * wheel_base_ / wheel_v_ * 1000); // 转弯持续时间 ms
        PublishVelocity(fabs(wheel_v / 2), wheel_v / wheel_base_, avoid_obstacle_rotate_duration_);
        avoid_obstacle_rotate_complete_ = true;
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleRotateSingleWheel(const std::vector<std::vector<uint8_t>> &grid, int height, int width, int y_slow)
{
    (void)y_slow;
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        !avoid_obstacle_rotate_complete_)
    {
        int obs_num_left = 0;
        int obs_num_right = 0;
        for (int y = height - 1; y >= 0; y--)
        {
            for (int l_x = 0; l_x <= left_line_x_; l_x++)
            {
                if (grid[y][l_x] == 1)
                {
                    obs_num_left++;
                }
            }
            for (int r_x = right_line_x_; r_x < width; r_x++)
            {
                if (grid[y][r_x] == 1)
                {
                    obs_num_right++;
                }
            }
        }
        if (obs_num_left <= obs_num_right)
        {
            rotation_w_ = wheel_v_;
        }
        else
        {
            rotation_w_ = -wheel_v_;
        }
        float rotate_angle = GenerateRandomNumber<float>(turning_angle_min_, turning_angle_max_);              // 计算转弯随机角度
        avoid_obstacle_rotate_duration_ = static_cast<uint64_t>(rotate_angle * wheel_base_ / wheel_v_ * 1000); // 转弯持续时间 ms
        PublishVelocity(fabs(rotation_w_ / 2), rotation_w_ / wheel_base_, avoid_obstacle_rotate_duration_);
        avoid_obstacle_rotate_complete_ = true;
    }
}

void NavigationRandomMowerAlg::UpdateTrapRotateVelocity(const std::vector<std::vector<uint8_t>> &grid, int height, int width)
{
    if (is_new_trap_rotate_)
    {
        // 已经进入trap rotate，则使用上一次的trap rotate速度
        LOG_INFO("use last trap rotate velocity: {}", trap_rotate_velocity_);
        rotation_w_ = trap_rotate_velocity_;
        return;
    }
    // 第一次进入trap rotate，则计算新的trap rotate速度
    int obs_num_left = 0;
    int obs_num_right = 0;
    for (int y = height - 1; y >= 0; y--)
    {
        for (int l_x = 0; l_x <= left_line_x_; l_x++)
        {
            if (grid[y][l_x] == 1)
            {
                obs_num_left++;
            }
        }
        for (int r_x = right_line_x_; r_x < width; r_x++)
        {
            if (grid[y][r_x] == 1)
            {
                obs_num_right++;
            }
        }
    }
    if (obs_num_left <= obs_num_right)
    {
        rotation_w_ = trap_spot_w_;
    }
    else
    {
        rotation_w_ = -trap_spot_w_;
    }
    trap_rotate_velocity_ = rotation_w_;
    is_new_trap_rotate_ = true;
    LOG_INFO("update new trap rotate velocity: {}", rotation_w_);
}

void NavigationRandomMowerAlg::ProcessTrapAvoidObstacleRotate(const std::vector<std::vector<uint8_t>> &grid, int height, int width)
{
    if (!avoid_obstacle_rotate_complete_)
    {
        if (!trap_rotate_start_)
        {
            UpdateTrapRotateVelocity(grid, height, width);
            trap_rotate_angle_ = GenerateRandomNumber<float>(trap_turning_angle_min_, trap_turning_angle_max_); // 计算转弯随机角度
            trap_rotate_timestamp_ = std::chrono::steady_clock::now();
            trap_rotate_start_ = true;
        }
        std::chrono::duration<double> rotate_duration = std::chrono::steady_clock::now() - trap_rotate_timestamp_;
        float rotate_angle = rotate_duration.count() * trap_spot_w_;
        if (rotate_angle < trap_rotate_angle_)
        {
            PublishVelocity(0, rotation_w_);
            LOG_INFO_THROTTLE(500, "Trap rotate target:{} , actual:{}", trap_rotate_angle_, rotate_angle);
        }
        else
        {
            PublishVelocity(0, 0);
            avoid_obstacle_rotate_complete_ = true;
        }
    }
}

void NavigationRandomMowerAlg::ResetTrapAvoidObstacleFlags()
{
    avoid_obstacle_slowdown_complete_ = false;
    avoid_obstacle_reverse_complete_ = false;
    avoid_obstacle_rotate_complete_ = false;
    trap_rotate_start_ = false;
    is_surrounded_ = false;
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleFlags()
{
    if (avoid_obstacle_slowdown_complete_ &&
        avoid_obstacle_reverse_complete_ &&
        avoid_obstacle_rotate_complete_)
    {
        avoid_obstacle_ = false;
        avoid_obstacle_slowdown_start_ = false;
        avoid_obstacle_slowdown_complete_ = false;
        avoid_obstacle_reverse_complete_ = false;
        avoid_obstacle_rotate_complete_ = false;
        have_obs_in_grass_ = false;
        is_surrounded_ = false;
        reach_edge_ = false;
    }
}

void NavigationRandomMowerAlg::ResetAvoidObstacleFlags()
{
    avoid_obstacle_ = false;
    avoid_obstacle_slowdown_start_ = false;
    avoid_obstacle_slowdown_complete_ = false;
    avoid_obstacle_reverse_complete_ = false;
    avoid_obstacle_rotate_complete_ = false;
    have_obs_in_grass_ = false;
    is_surrounded_ = false;
    reach_edge_ = false;
}

// when obs_dis is less than stop_slow_down_dis whether should add zeros vel publish
void NavigationRandomMowerAlg::ProcessAvoidObstacleSlowdown()
{
    if (!avoid_obstacle_slowdown_complete_)
    {
        if (!avoid_obstacle_slowdown_start_)
        {
            avoid_obstacle_slowdown_vel_linear_ = random_v_;
            avoid_obstacle_slowdown_start_ = true;
        }
        float temp = 0.5 * (random_v_ * random_v_) / (start_slow_down_dis_ - stop_slow_down_dis_) * slow_down_time_gap_;
        float vel_linear = avoid_obstacle_slowdown_vel_linear_ - temp;
        if (vel_linear < 0)
        {
            vel_linear = 0;
        }
        else
        {
            avoid_obstacle_slowdown_vel_linear_ = vel_linear;
        }
        PublishVelocity(vel_linear, 0);
        if (fabs(vel_linear) < 1e-6)
        {
            PublishZeroVelocity();
            avoid_obstacle_slowdown_start_ = false;
            avoid_obstacle_slowdown_complete_ = true;
        }
    }
}

void NavigationRandomMowerAlg::ProcessAvoidObstacleSlowdown(int height, int y_obs, float res)
{
    if (!avoid_obstacle_slowdown_complete_)
    {
        float obs_dis = (height - y_obs) * res + dead_zone_;
        LOG_WARN_THROTTLE(100, "****obs_dis:{},y_obs:{},dead_zone:{}", obs_dis, y_obs, dead_zone_);
        if (obs_dis <= stop_slow_down_dis_ - 0.05)
        {
            PublishZeroVelocity();
            avoid_obstacle_slowdown_complete_ = true;
        }
        else
        {
            if (obs_dis > start_slow_down_dis_)
            {
                PublishZeroVelocity();
                avoid_obstacle_slowdown_complete_ = true;
            }
            else // normally obs_dis should be less than  start_slow_down_dis_
            {
                float vel_linear = random_v_ * (obs_dis - stop_slow_down_dis_) / (start_slow_down_dis_ - stop_slow_down_dis_);
                LOG_WARN_THROTTLE(100, "****vel_linear:{},", vel_linear);
                PublishVelocity(vel_linear, 0);
                if (fabs(vel_linear) < 1e-1)
                {
                    PublishZeroVelocity();
                    avoid_obstacle_slowdown_complete_ = true;
                }
            }
        }
    }
}

void NavigationRandomMowerAlg::ProhibitVelPublisher()
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(0, 0);
        vel_publisher_->SetProhibitFlag(true);
    }
}

void NavigationRandomMowerAlg::PublishVelocity(float linear, float angular, uint64_t duration_ms)
{
    if (vel_publisher_)
    {
        vel_publisher_->PubVelocity(linear, angular, duration_ms);
        if (duration_ms > 0)
        {
            while (!vel_publisher_->IsExecutionCompleted())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
        }
    }
}

void NavigationRandomMowerAlg::AdjustHeading(float turning_angle_offset)
{
    LOG_INFO("AdjustHeading:{}", turning_angle_offset);
    if (vel_publisher_)
    {
        vel_publisher_->AdjustHeading(turning_angle_offset);
    }
}

void NavigationRandomMowerAlg::PublishZeroVelocity()
{
    PublishVelocity(0, 0);
}

void NavigationRandomMowerAlg::PublishRandomMowerState(RandomMowerRunningState state)
{
    if (random_mower_state_callback_)
    {
        random_mower_state_callback_(state);
    }
}

void NavigationRandomMowerAlg::PauseVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->PauseVelocity();
    }
}

void NavigationRandomMowerAlg::ResumeVelocity()
{
    if (vel_publisher_)
    {
        vel_publisher_->ResumeVelocity();
    }
}

void NavigationRandomMowerAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.node_name = "navigation_random_mowing_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception)
            .or_else([](auto &error) {
                LOG_ERROR("Navigation random_mowing publish soc exception Unable to publishCopyOf, error: {}", error);
            });
    }
}

void NavigationRandomMowerAlg::SetFusionPose(const ob_mower_msgs::NavFusionPose &fusion_pose)
{
    if (vel_publisher_)
    {
        vel_publisher_->SetFusionPose(fusion_pose);
    }

    {
        std::lock_guard<std::mutex> lock(m_fusion_pose_mtx_);
        m_newest_pose_ = fusion_pose;
    }
}

void NavigationRandomMowerAlg::SetOccupancyResult(const OccupancyResult &occupancy_result, uint64_t output_timestamp)
{
    if (vel_publisher_)
    {
        vel_publisher_->SetOccupancyResult(occupancy_result);
    }
    std::lock_guard<std::mutex> lock(data_time_info_map_mtx_);
    auto iter = data_time_info_map_.find("Bev");
    if (iter == data_time_info_map_.end())
    {
        return;
    }
    uint64_t low_freq_time = 200000;
    uint32_t low_freq_count_max = 10;
    auto last_time_info = iter->second;
    UpdateDataTimeInfo("Bev", last_time_info, low_freq_time, low_freq_count_max, output_timestamp, data_time_info_map_["Bev"]);
}

void NavigationRandomMowerAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;

    pub_exception_ = std::make_unique<iox_exception_publisher>(
        iox::capro::ServiceDescription{kSocExceptionIox[0],
                                       kSocExceptionIox[1],
                                       kSocExceptionIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
}

void NavigationRandomMowerAlg::check_trap(ObstacleDetectionResult result)
{
    if (is_danger_num_ >= danger_threshold_ && !is_trap_)
    {
        is_trap_ = true;
        trap_forward_num_ = 0;
    }
    if (!is_trap_)
    {
        if (result.is_danger)
        {
            is_danger_num_ += 1;
        }
        else
        {
            is_danger_num_ -= 1;
            if (is_danger_num_ < 0)
            {
                is_danger_num_ = 0;
            }
        }
    }
    else // trap
    {
        if (!mark_trap_timestamp_)
        {
            trap_timestamp_ = std::chrono::steady_clock::now();
            mark_trap_timestamp_ = true;
        }

        std::chrono::duration<double> trap_duration = std::chrono::steady_clock::now() - trap_timestamp_;
        if (trap_duration.count() >= max_trap_duration_)
        {
            PublishException(mower_msgs::msg::SocExceptionLevel::ERROR, mower_msgs::msg::SocExceptionValue::ALG_PNC_RANDOM_MOWING_TRAP_EXCEPTION);
            LOG_ERROR_THROTTLE(1000, "random mowing trap!!");
        }

        if (trap_forward_num_ >= straight_threshold_)
        {
            is_trap_ = false;
            mark_trap_timestamp_ = false;
            is_danger_num_ = 0;
        }

        // if (!result.is_danger && !result.is_slow)
        // {
        //     straight_num_ += 1;
        //     LOG_ERROR("straight_num_ : {}---------", straight_num_);
        // }
        // else
        // {
        //     straight_num_ = 0;
        // }
    }
}

} // namespace fescue_iox
