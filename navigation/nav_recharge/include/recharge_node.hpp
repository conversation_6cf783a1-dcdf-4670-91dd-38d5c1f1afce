#ifndef NAVIGATION_RECHARGE_NODE_HPP
#define NAVIGATION_RECHARGE_NODE_HPP

#include "data_type.hpp"
#include "geometry_msgs/twist__struct.h"
#include "mower_msgs/msg/charge_pile_dock_status.hpp"
#include "mower_msgs/msg/mcu_exception.hpp"
#include "mower_msgs/msg/mcu_mission_info.hpp"
#include "mower_msgs/msg/mcu_sensor.hpp"
#include "ob_mower_msgs/charge_mark_detect_result.h"
#include "ob_mower_msgs/charge_result_struct.h"
#include "ob_mower_msgs/mark_location_result_struct.h"
#include "ob_mower_msgs/nav_alg_ctrl__struct.h"
#include "ob_mower_msgs/nav_cross_region_final_result__struct.h"
#include "ob_mower_msgs/nav_cross_region_state__struct.h"
#include "ob_mower_msgs/nav_recharge_final_result__struct.h"
#include "ob_mower_msgs/nav_recharge_state__struct.h"
#include "ob_mower_msgs/nav_running_state__struct.h"
#include "ob_mower_msgs/qrcode_result__struct.h"
#include "ob_mower_srvs/node_common_param_service.h"
#include "recharge.hpp"
#include "recharge_config.hpp"
#include "std_msgs/int32__struct.h"
#include "utils/file.hpp"
#include "utils/heartbeat_publisher.hpp"
#include "utils/iceoryx_header.hpp"
#include "utils/thread_safe_queue.hpp"

#include <chrono>
#include <cmath>
#include <memory>
#include <mutex>
#include <queue>
#include <sys/prctl.h>
#include <thread>

namespace fescue_iox
{

class NavigationRechargeNode
{
    using iox_twist_publisher = iox::popo::Publisher<geometry_msgs__msg__Twist_iox>;
    using iox_nav_alg_ctrl_publisher = iox::popo::Publisher<fescue_msgs__msg__NavigationAlgoCtrlData>;
    using iox_nav_recharge_result_publisher = iox::popo::Publisher<fescue_msgs__msg__NavRechargeFinalResult>;

    using get_node_param_request = ob_mower_srvs::GetNodeParamRequest;
    using get_node_param_response = ob_mower_srvs::GetNodeParamResponse;
    using set_node_param_request = ob_mower_srvs::SetNodeParamRequest;
    using set_node_param_response = ob_mower_srvs::SetNodeParamResponse;

public:
    NavigationRechargeNode(const std::string &node_name);
    ~NavigationRechargeNode();

private:
    void InitWorkingDirectory();
    void InitParam();
    void InitAlgorithmParam();
    void ConfigParamToAlgorithmParam(const NavigationRechargeAlgConfig &config, RechargeAlgParam &param);
    void InitLogger();
    void InitPublisher();
    void InitSubscriber();
    void InitAlgorithm();
    void InitService();
    void DeinitAlgorithm();
    void CheckMCUExeceptionTimeout();
    void InitHeartbeat();

private:
    void DealMCUSensor(const mower_msgs::msg::McuSensor &data);
    void DealMCUException(const mower_msgs::msg::McuException &data);
    void DealChargeDetectResult(const fescue_msgs__msg__ChargeResult &msg);
    void DealQRCodeLocationResult(const fescue_msgs__msg__QrCodeResult &msg);
    void DealMarkLocationResult(const fescue_msgs__msg__MarkLocationResult &msg);
    void DealNavAlgCtrlResult(const fescue_msgs__msg__NavigationAlgoCtrlData &msg);
    void DealChargePileDockStatus(const mower_msgs::msg::ChargePileDockStatus &msg);
    void DealNavRunningState(const fescue_msgs__msg__NavigationRunningStateData &data);
    void RechargeThread();
    void ResetSubData();
    bool GetRechargeNodeParam(ob_mower_srvs::NodeParamData &data);
    bool SetRechargeNodeParam(const ob_mower_srvs::NodeParamData &data);
    void PublishRechargeFinalResult(const RechargeAlgResult &result);
    void SetRechargeVelPublisherProhibit(bool prohibit)
    {
        if (recharge_alg_)
        {
            recharge_alg_->SetVelPublisherProhibit(prohibit);
        }
    }
    void DealRechargeRunningStateCallback(RechargeRunningState state);
    void DealNavFusionPose(const ob_mower_msgs::NavFusionPose &data);
    void DealEdgeFollowStatus(const std_msgs__msg__Int32_iox &data);

private:
    // subscriber
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__MarkLocationResult>> sub_mark_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__ChargeMarkDetectResult>> sub_charge_detect_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__QrCodeResult>> sub_qrcode_loc_result_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationAlgoCtrlData>> sub_nav_alg_ctrl_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<fescue_msgs__msg__NavigationRunningStateData>> sub_nav_running_state_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuSensor>> sub_mcu_sensor_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<mower_msgs::msg::McuException>> sub_mcu_exception_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<ob_mower_msgs::NavFusionPose>> sub_nav_fusion_pose_{nullptr};
    std::unique_ptr<IceoryxSubscriberMower<std_msgs__msg__Int32_iox>> sub_edge_follow_status_{nullptr};

    // publisher
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs__msg__NavRechargeFinalResult>> pub_nav_recharge_final_result_{nullptr};
    std::unique_ptr<IceoryxPublisherMower<fescue_msgs__msg__RechargeStateData>> pub_recharge_state_{nullptr};

    // service
    std::unique_ptr<IceoryxServerMower<get_node_param_request, get_node_param_response>> service_get_node_param_{nullptr};
    std::unique_ptr<IceoryxServerMower<set_node_param_request, set_node_param_response>> service_set_node_param_{nullptr};

private:
    std::string node_name_{"navigation_recharge_node"};
    std::string log_dir_{"/userdata/log"};
    std::string console_log_level_{"info"};
    std::string file_log_level_{"warn"};
    std::string recharge_alg_conf_file_{"conf/navigation_recharge_node/recharge.yaml"};

    std::thread recharge_thread_;
    std::atomic_bool thread_running_{true};
    std::atomic_bool recharge_enable_{false};
    std::mutex station_mtx_;
    std::mutex qrcode_loc_mtx_;
    std::mutex mcu_exception_mutex_;
    std::mutex mark_loc_mtx_;

    MarkLocationResult mark_loc_result_;
    McuExceptionStatus mcu_exception_status_{McuExceptionStatus::NORMAL};
    ChargeStationDetectResult charge_station_result_;
    QRCodeLocationResult qrcode_loc_result_;
    std::chrono::steady_clock::time_point last_mcu_exception_time_;
    RechargeAlgParam recharge_alg_param_;
    std::unique_ptr<NavigationRechargeAlg> recharge_alg_{nullptr};
    std::unique_ptr<FileWriter> writer_{nullptr};
    std::unique_ptr<NodeHeartbeatPublisher> pub_heartbeat_{nullptr};
    bool charge_terminal_status_{false};
};

} // namespace fescue_iox

#endif
