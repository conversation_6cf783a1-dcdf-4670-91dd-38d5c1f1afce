#include "charge_mark_detection.hpp"

#include "charge_mark_detection_config.hpp"
#include "utils/dir.hpp"
#include "utils/logger.hpp"
#include "utils/time.hpp"
#include "utils/utils.hpp"

namespace fescue_iox
{

ChargeMarkDetectionAlg::ChargeMarkDetectionAlg(const std::string &conf_file)
    : conf_file_(conf_file)
{
    InitPublisher();
    InitAlg();
}

ChargeMarkDetectionAlg::~ChargeMarkDetectionAlg()
{
    LOG_WARN("ChargeMarkDetectionAlg start stop!");
    delete[] output_result_.debug_img.data;
    ChargeMarkDetectorRelease(&alg_handle_);
    LOG_WARN("ChargeMarkDetectionAlg stop success!");
}

bool ChargeMarkDetectionAlg::DoChargeMarkDetect(const sensor_msgs__msg__Image_iox &image)
{
    PreChargeMarkDetect(image);

    output_result_.reset();
    int result = ChargeMarkDetectorExecute(&alg_handle_, &input_image_, output_result_);
    if (result != CHARGE_MARK_DET_SUCCESS)
    {
        LOG_ERROR("Perception charge mark detection execute fail, error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_CROSS_REGION_DETECTION_EXECUTE_ERROR_EXCEPTION);
        return false;
    }

    PostChargeMarkDetect();

    return true;
}

void ChargeMarkDetectionAlg::PostChargeMarkDetect()
{
    PublishChargeMarkResult();
    PublishDebugImage(output_result_.debug_img, "bgr8");
}

bool ChargeMarkDetectionAlg::SetAlgParam(ChargeMarkInputParams &params)
{
    int result = ChargeMarkDetectorSetParams(&alg_handle_, params);
    if (result != CHARGE_MARK_DET_SUCCESS)
    {
        LOG_ERROR("Set perception charge mark detection alg param fail, error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::WARNING,
                         mower_msgs::msg::SocExceptionValue::ALG_CROSS_REGION_DETECTION_PARAM_ERROR_EXCEPTION);
        return false;
    }

    LOG_INFO("Set charge and mark detection params success!");
    ChargeMarkDetectionAlgConfig config = Config<ChargeMarkDetectionAlgConfig>::GetConfig();
    config.charge_config.nms_threshold = params.charge_params.nms_threshold;
    config.charge_config.prob_threshold = params.charge_params.prob_threshold;
    config.charge_config.direction_threshold = params.charge_params.direction_threshold;
    config.charge_config.pose_threshold = params.charge_params.pose_threshold;
    config.charge_config.pose_threshold_x = params.charge_params.pose_threshold_x;
    config.charge_config.pose_threshold_y = params.charge_params.pose_threshold_y;
    config.charge_config.head_range_0 = params.charge_params.head_range_0;
    config.charge_config.station_range_0 = params.charge_params.station_range_0;
    config.charge_config.pose_mode = params.charge_params.pose_mode;
    config.charge_config.range_mode = params.charge_params.range_mode;

    config.mark_config.prob_threshold = params.mark_params.prob_threshold;
    config.mark_config.nms_threshold = params.mark_params.nms_threshold;
    config.mark_config.direction_threshold = params.mark_params.direction_threshold;
    config.mark_config.area_threshold = params.mark_params.area_threshold;
    config.mark_config.range_threshold = params.mark_params.range_threshold;

    config.common_config.show_image = params.show_image;
    config.common_config.log_level = params.log_level;
    config.common_config.save_mode = params.save_mode;
    config.common_config.save_path = params.save_path;
    config.common_config.model_path = params.model_path;
    config.common_config.ignore_num = params.ignore_num;
    config.common_config.core_id = params.core_id;

    Config<ChargeMarkDetectionAlgConfig>::SetConfig(config);
    LOG_INFO("New charge and mark detection alg params: {}", config.toString().c_str());
    return true;
}

ChargeMarkInputParams ChargeMarkDetectionAlg::GetAlgParam()
{
    return ChargeMarkDetectorGetParams(&alg_handle_);
}

const char *ChargeMarkDetectionAlg::GetAlgVersion()
{
    return ChargeMarkDetectorGetVersion();
}

bool ChargeMarkDetectionAlg::InitAlg()
{
    InitAlgParam();
    output_result_.debug_img.data = new uint8_t[MAX_IMG_BUFF_SIZE];

    ChargeMarkDetectionAlgConfig config = Config<ChargeMarkDetectionAlgConfig>::GetConfig();
    ChargeMarkInputParams param;
    param.mark_params.prob_threshold = config.mark_config.prob_threshold;
    param.mark_params.nms_threshold = config.mark_config.nms_threshold;
    param.mark_params.direction_threshold = config.mark_config.direction_threshold;
    param.mark_params.area_threshold = config.mark_config.area_threshold;
    param.mark_params.range_threshold = config.mark_config.range_threshold;

    param.charge_params.prob_threshold = config.charge_config.prob_threshold;
    param.charge_params.nms_threshold = config.charge_config.nms_threshold;
    param.charge_params.direction_threshold = config.charge_config.direction_threshold;
    param.charge_params.pose_threshold = config.charge_config.pose_threshold;
    param.charge_params.pose_threshold_x = config.charge_config.pose_threshold_x;
    param.charge_params.pose_threshold_y = config.charge_config.pose_threshold_y;
    param.charge_params.head_range_0 = config.charge_config.head_range_0;
    param.charge_params.station_range_0 = config.charge_config.station_range_0;
    param.charge_params.range_mode = config.charge_config.range_mode;
    param.charge_params.pose_mode = config.charge_config.pose_mode;

    param.show_image = config.common_config.show_image;
    param.log_level = config.common_config.log_level;
    param.save_mode = config.common_config.save_mode;
    param.save_path = config.common_config.save_path;
    param.ignore_num = config.common_config.ignore_num;
    param.model_path = config.common_config.model_path;
    param.core_id = config.common_config.core_id;

    int result = ChargeMarkDetectorCreatFromStruct(&alg_handle_, param);
    if (result != CHARGE_MARK_DET_SUCCESS)
    {
        LOG_ERROR("Perception charge mark detection algorithm initialization fail, error code: {:X}", result);
        PublishException(mower_msgs::msg::SocExceptionLevel::ERROR,
                         mower_msgs::msg::SocExceptionValue::ALG_CROSS_REGION_DETECTION_INIT_EXCEPTION);
        return false;
    }
    LOG_INFO("ob_mower_charge_mark_detection alg version: {}", ChargeMarkDetectorGetVersion());
    return true;
}

void ChargeMarkDetectionAlg::InitAlgParam()
{
    std::string conf_path = GetDirectoryPath(conf_file_);
    if (!conf_path.empty())
    {
        LOG_INFO("Perception charge mark detection create alg config path: {}", conf_path.c_str());
        if (!CreateDirectories(conf_path))
        {
            LOG_ERROR("Perception charge mark detection detect create alg config path failed!");
        }
    }
    if (!Config<ChargeMarkDetectionAlgConfig>::Init(conf_file_))
    {
        LOG_WARN("Init perception charge mark detection alg config parameters failed!");
    }
    ChargeMarkDetectionAlgConfig config = Config<ChargeMarkDetectionAlgConfig>::GetConfig();
    LOG_INFO("{}", config.toString().c_str());
    if (!Config<ChargeMarkDetectionAlgConfig>::SetConfig(config, true))
    {
        LOG_WARN("Set perception charge mark detection alg config parameters failed!");
    }
}

void ChargeMarkDetectionAlg::InitPublisher()
{
    iox::popo::PublisherOptions options_pub;
    options_pub.subscriberTooSlowPolicy = iox::popo::ConsumerTooSlowPolicy::DISCARD_OLDEST_DATA;
    pub_mark_result_ = std::make_unique<iox_mark_result_publisher>(
        iox::capro::ServiceDescription{kPerceptionMarkDetectResultIox[0],
                                       kPerceptionMarkDetectResultIox[1],
                                       kPerceptionMarkDetectResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_charge_result_ = std::make_unique<iox_charge_result_publisher>(
        iox::capro::ServiceDescription{kPerceptionChargeDetectResultIox[0],
                                       kPerceptionChargeDetectResultIox[1],
                                       kPerceptionChargeDetectResultIox[2],
                                       {0U, 0U, 0U, 0U},
                                       iox::capro::Interfaces::INTERNAL},
        options_pub);
    pub_debug_img_ = std::make_unique<iox_image_publisher>(
        iox::capro::ServiceDescription{"orbbec", "fescue_iox", "charge_mark_detect_image", {0U, 0U, 0U, 0U}, iox::capro::Interfaces::INTERNAL},
        options_pub);

    pub_charge_mark_result_ = std::make_unique<IceoryxPublisherMower<fescue_msgs__msg__ChargeMarkDetectResult>>("charge_mark_detect_result");
    pub_exception_ = std::make_unique<IceoryxPublisherMower<mower_msgs::msg::SocException>>("soc_exception");
}

void ChargeMarkDetectionAlg::PreChargeMarkDetect(const sensor_msgs__msg__Image_iox &image)
{
    sec_ = image.header.stamp.sec;
    nanosec_ = image.header.stamp.nanosec;
    frame_id_ = std::string(image.header.frame_id.c_str());
    encoding_ = std::string(image.encoding.c_str());
    timestamp_ms_ = sec_ * 1000 + nanosec_ / 1000000;

    input_image_.timestamp = timestamp_ms_;
    input_image_.data = (uint8_t *)image.data.data();
    input_image_.width = image.width;
    input_image_.height = image.height;
    input_image_.channels = 1;
    input_image_.size = input_image_.width * input_image_.height * 3 / 2; // YUV frame size
    input_image_.colorOrder = ColorOrder::OB_FMT_YUV420SP;
}

void ChargeMarkDetectionAlg::PublishChargeResult()
{
    LOG_DEBUG("Charge detect result: is_charge {} is_head {}",
              output_result_.chargestation.is_chargestation,
              output_result_.chargestation.is_head);

    if (pub_charge_result_->hasSubscribers())
    {
        auto loan = pub_charge_result_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->timestamp = output_result_.timestamp;
            msg->is_charge = output_result_.chargestation.is_chargestation;
            msg->is_head = output_result_.chargestation.is_head;
            msg->direction = output_result_.chargestation.direction;
            msg->pose = output_result_.chargestation.pose;
            msg->range = output_result_.chargestation.range;
            msg->width = output_result_.width_height.at(0);
            msg->height = output_result_.width_height.at(1);
            size_t head_box_size = std::min(IOX_MAX_HEAD_BOX_SIZE, static_cast<int>(output_result_.chargestation.head_box.size()));
            for (size_t i = 0; i < head_box_size; i++)
            {
                msg->head_box[i] = output_result_.chargestation.head_box[i];
            }
            size_t station_box_size = std::min(IOX_MAX_STATION_BOX_SIZE, static_cast<int>(output_result_.chargestation.station_box.size()));
            for (size_t i = 0; i < station_box_size; i++)
            {
                msg->station_box[i] = output_result_.chargestation.station_box[i];
            }
            msg.publish();
        }
    }
}

void ChargeMarkDetectionAlg::PublishMarkResult()
{
    LOG_DEBUG("Mark detect result: is_mark {} direction {} box size {}",
              output_result_.mark.is_mark, output_result_.mark.direction,
              output_result_.mark.mark_box.size());

    if (pub_mark_result_->hasSubscribers())
    {
        auto loan = pub_mark_result_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->timestamp = output_result_.timestamp;
            msg->is_mark = output_result_.mark.is_mark;
            msg->direction = output_result_.mark.direction;
            msg->range = output_result_.mark.range;
            if (output_result_.mark.mark_box.size() == 5)
            {
                msg->confidence = output_result_.mark.mark_box[4];
                for (size_t i = 0; i < 4; i++)
                {
                    msg->mark_box[i] = output_result_.mark.mark_box[i];
                }
            }
            msg->width = output_result_.width_height.at(0);
            msg->height = output_result_.width_height.at(1);
            msg.publish();
        }
    }
}

void ChargeMarkDetectionAlg::PublishChargeMarkResult()
{
    if (!pub_charge_mark_result_->hasSubscribers())
    {
        return; // Early exit if no subscribers
    }

    fescue_msgs__msg__ChargeMarkDetectResult msg;
    msg.timestamp_ms = output_result_.timestamp;

    // charge result
    msg.charge_result.timestamp = output_result_.timestamp;
    msg.charge_result.is_charge = output_result_.chargestation.is_chargestation;
    msg.charge_result.is_head = output_result_.chargestation.is_head;
    msg.charge_result.direction = output_result_.chargestation.direction;
    msg.charge_result.pose = output_result_.chargestation.pose;
    msg.charge_result.range = output_result_.chargestation.range;
    msg.charge_result.width = output_result_.width_height.at(0);
    msg.charge_result.height = output_result_.width_height.at(1);
    size_t head_box_size = std::min(IOX_MAX_HEAD_BOX_SIZE, static_cast<int>(output_result_.chargestation.head_box.size()));
    for (size_t i = 0; i < head_box_size; i++)
    {
        msg.charge_result.head_box[i] = output_result_.chargestation.head_box[i];
    }
    size_t station_box_size = std::min(IOX_MAX_STATION_BOX_SIZE, static_cast<int>(output_result_.chargestation.station_box.size()));
    for (size_t i = 0; i < station_box_size; i++)
    {
        msg.charge_result.station_box[i] = output_result_.chargestation.station_box[i];
    }

    // mark result
    msg.mark_result.timestamp = output_result_.timestamp;
    msg.mark_result.is_mark = output_result_.mark.is_mark;
    msg.mark_result.direction = output_result_.mark.direction;
    msg.mark_result.range = output_result_.mark.range;
    if (output_result_.mark.mark_box.size() == 5)
    {
        msg.mark_result.confidence = output_result_.mark.mark_box[4];
        for (size_t i = 0; i < 4; i++)
        {
            msg.mark_result.mark_box[i] = output_result_.mark.mark_box[i];
        }
    }
    msg.mark_result.width = output_result_.width_height.at(0);
    msg.mark_result.height = output_result_.width_height.at(1);

    pub_charge_mark_result_->publish(msg);
}

void ChargeMarkDetectionAlg::PublishDebugImage(const ImageBuffer &image, const std::string &encoding)
{
    LOG_DEBUG("ChargeMarkDetectionAlg image size {}", image.size);
    if (image.size > 0 && pub_debug_img_->hasSubscribers())
    {
        auto loan = pub_debug_img_->loan();
        if (!loan.has_error())
        {
            auto &msg = loan.value();
            msg->header.stamp.sec = sec_;
            msg->header.stamp.nanosec = nanosec_;
            msg->header.frame_id.unsafe_assign(frame_id_.c_str());
            msg->width = image.width;
            msg->height = image.height;
            msg->step = msg->width * image.channels;
            msg->encoding.unsafe_assign(encoding.c_str());
            msg->is_bigendian = false;
            size_t img_size = (msg->step * msg->height > IOX_IMAGE_DATA_MAX) ? IOX_IMAGE_DATA_MAX : (msg->step * msg->height);
            msg->data.resize(img_size);
            memcpy(msg->data.data(), image.data, img_size);
            msg.publish();
        }
    }
}

void ChargeMarkDetectionAlg::PublishException(mower_msgs::msg::SocExceptionLevel level, mower_msgs::msg::SocExceptionValue value)
{
    if (pub_exception_)
    {
        mower_msgs::msg::SocException exception;
        exception.timestamp = GetSteadyClockTimestampMs();
        exception.node_name = "perception_charge_mark_detection_node";
        exception.exception_level = level;
        exception.exception_value = value;
        pub_exception_->publishCopyOf(exception);
    }
}

} // namespace fescue_iox